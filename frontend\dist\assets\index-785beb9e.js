import{r as P,a as Dr,R as Fr,b as oe,g as Nt,L as z,N as Be,u as jr,c as jt,d as zr,e as Hr,f as Ur,h as _e,B as Br}from"./vendor-8ad64ce2.js";import{_ as $r,a as qr}from"./utils-ac32d1bb.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const s of n)if(s.type==="childList")for(const l of s.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function i(n){const s={};return n.integrity&&(s.integrity=n.integrity),n.referrerPolicy&&(s.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?s.credentials="include":n.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(n){if(n.ep)return;n.ep=!0;const s=i(n);fetch(n.href,s)}})();var hr={exports:{}},wt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qr=P,Kr=Symbol.for("react.element"),Vr=Symbol.for("react.fragment"),Yr=Object.prototype.hasOwnProperty,Gr=Qr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Wr={key:!0,ref:!0,__self:!0,__source:!0};function mr(t,a,i){var r,n={},s=null,l=null;i!==void 0&&(s=""+i),a.key!==void 0&&(s=""+a.key),a.ref!==void 0&&(l=a.ref);for(r in a)Yr.call(a,r)&&!Wr.hasOwnProperty(r)&&(n[r]=a[r]);if(t&&t.defaultProps)for(r in a=t.defaultProps,a)n[r]===void 0&&(n[r]=a[r]);return{$$typeof:Kr,type:t,key:s,ref:l,props:n,_owner:Gr.current}}wt.Fragment=Vr;wt.jsx=mr;wt.jsxs=mr;hr.exports=wt;var zt=hr.exports;const ie=zt.Fragment,e=zt.jsx,c=zt.jsxs;var Rt={},Yt=Dr;Rt.createRoot=Yt.createRoot,Rt.hydrateRoot=Yt.hydrateRoot;function Je(t,a){t.prototype=Object.create(a.prototype),t.prototype.constructor=t,$r(t,a)}var Ze=function(){function t(){this.listeners=[]}var a=t.prototype;return a.subscribe=function(r){var n=this,s=r||function(){};return this.listeners.push(s),this.onSubscribe(),function(){n.listeners=n.listeners.filter(function(l){return l!==s}),n.onUnsubscribe()}},a.hasListeners=function(){return this.listeners.length>0},a.onSubscribe=function(){},a.onUnsubscribe=function(){},t}();function J(){return J=Object.assign?Object.assign.bind():function(t){for(var a=1;a<arguments.length;a++){var i=arguments[a];for(var r in i)({}).hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},J.apply(null,arguments)}var dt=typeof window>"u";function ke(){}function Xr(t,a){return typeof t=="function"?t(a):t}function xt(t){return typeof t=="number"&&t>=0&&t!==1/0}function ht(t){return Array.isArray(t)?t:[t]}function fr(t,a){return Math.max(t+(a||0)-Date.now(),0)}function st(t,a,i){return kt(t)?typeof a=="function"?J({},i,{queryKey:t,queryFn:a}):J({},a,{queryKey:t}):t}function je(t,a,i){return kt(t)?[J({},a,{queryKey:t}),i]:[t||{},a]}function Jr(t,a){if(t===!0&&a===!0||t==null&&a==null)return"all";if(t===!1&&a===!1)return"none";var i=t??!a;return i?"active":"inactive"}function Gt(t,a){var i=t.active,r=t.exact,n=t.fetching,s=t.inactive,l=t.predicate,o=t.queryKey,u=t.stale;if(kt(o)){if(r){if(a.queryHash!==Ht(o,a.options))return!1}else if(!mt(a.queryKey,o))return!1}var h=Jr(i,s);if(h==="none")return!1;if(h!=="all"){var m=a.isActive();if(h==="active"&&!m||h==="inactive"&&m)return!1}return!(typeof u=="boolean"&&a.isStale()!==u||typeof n=="boolean"&&a.isFetching()!==n||l&&!l(a))}function Wt(t,a){var i=t.exact,r=t.fetching,n=t.predicate,s=t.mutationKey;if(kt(s)){if(!a.options.mutationKey)return!1;if(i){if(He(a.options.mutationKey)!==He(s))return!1}else if(!mt(a.options.mutationKey,s))return!1}return!(typeof r=="boolean"&&a.state.status==="loading"!==r||n&&!n(a))}function Ht(t,a){var i=(a==null?void 0:a.queryKeyHashFn)||He;return i(t)}function He(t){var a=ht(t);return Zr(a)}function Zr(t){return JSON.stringify(t,function(a,i){return Lt(i)?Object.keys(i).sort().reduce(function(r,n){return r[n]=i[n],r},{}):i})}function mt(t,a){return pr(ht(t),ht(a))}function pr(t,a){return t===a?!0:typeof t!=typeof a?!1:t&&a&&typeof t=="object"&&typeof a=="object"?!Object.keys(a).some(function(i){return!pr(t[i],a[i])}):!1}function ft(t,a){if(t===a)return t;var i=Array.isArray(t)&&Array.isArray(a);if(i||Lt(t)&&Lt(a)){for(var r=i?t.length:Object.keys(t).length,n=i?a:Object.keys(a),s=n.length,l=i?[]:{},o=0,u=0;u<s;u++){var h=i?u:n[u];l[h]=ft(t[h],a[h]),l[h]===t[h]&&o++}return r===s&&o===r?t:l}return a}function ea(t,a){if(t&&!a||a&&!t)return!1;for(var i in t)if(t[i]!==a[i])return!1;return!0}function Lt(t){if(!Xt(t))return!1;var a=t.constructor;if(typeof a>"u")return!0;var i=a.prototype;return!(!Xt(i)||!i.hasOwnProperty("isPrototypeOf"))}function Xt(t){return Object.prototype.toString.call(t)==="[object Object]"}function kt(t){return typeof t=="string"||Array.isArray(t)}function ta(t){return new Promise(function(a){setTimeout(a,t)})}function Jt(t){Promise.resolve().then(t).catch(function(a){return setTimeout(function(){throw a})})}function yr(){if(typeof AbortController=="function")return new AbortController}var ra=function(t){Je(a,t);function a(){var r;return r=t.call(this)||this,r.setup=function(n){var s;if(!dt&&((s=window)!=null&&s.addEventListener)){var l=function(){return n()};return window.addEventListener("visibilitychange",l,!1),window.addEventListener("focus",l,!1),function(){window.removeEventListener("visibilitychange",l),window.removeEventListener("focus",l)}}},r}var i=a.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},i.setEventListener=function(n){var s,l=this;this.setup=n,(s=this.cleanup)==null||s.call(this),this.cleanup=n(function(o){typeof o=="boolean"?l.setFocused(o):l.onFocus()})},i.setFocused=function(n){this.focused=n,n&&this.onFocus()},i.onFocus=function(){this.listeners.forEach(function(n){n()})},i.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},a}(Ze),Xe=new ra,aa=function(t){Je(a,t);function a(){var r;return r=t.call(this)||this,r.setup=function(n){var s;if(!dt&&((s=window)!=null&&s.addEventListener)){var l=function(){return n()};return window.addEventListener("online",l,!1),window.addEventListener("offline",l,!1),function(){window.removeEventListener("online",l),window.removeEventListener("offline",l)}}},r}var i=a.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},i.setEventListener=function(n){var s,l=this;this.setup=n,(s=this.cleanup)==null||s.call(this),this.cleanup=n(function(o){typeof o=="boolean"?l.setOnline(o):l.onOnline()})},i.setOnline=function(n){this.online=n,n&&this.onOnline()},i.onOnline=function(){this.listeners.forEach(function(n){n()})},i.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},a}(Ze),lt=new aa;function na(t){return Math.min(1e3*Math.pow(2,t),3e4)}function pt(t){return typeof(t==null?void 0:t.cancel)=="function"}var gr=function(a){this.revert=a==null?void 0:a.revert,this.silent=a==null?void 0:a.silent};function ot(t){return t instanceof gr}var vr=function(a){var i=this,r=!1,n,s,l,o;this.abort=a.abort,this.cancel=function(N){return n==null?void 0:n(N)},this.cancelRetry=function(){r=!0},this.continueRetry=function(){r=!1},this.continue=function(){return s==null?void 0:s()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(N,v){l=N,o=v});var u=function(v){i.isResolved||(i.isResolved=!0,a.onSuccess==null||a.onSuccess(v),s==null||s(),l(v))},h=function(v){i.isResolved||(i.isResolved=!0,a.onError==null||a.onError(v),s==null||s(),o(v))},m=function(){return new Promise(function(v){s=v,i.isPaused=!0,a.onPause==null||a.onPause()}).then(function(){s=void 0,i.isPaused=!1,a.onContinue==null||a.onContinue()})},f=function N(){if(!i.isResolved){var v;try{v=a.fn()}catch(d){v=Promise.reject(d)}n=function(w){if(!i.isResolved&&(h(new gr(w)),i.abort==null||i.abort(),pt(v)))try{v.cancel()}catch{}},i.isTransportCancelable=pt(v),Promise.resolve(v).then(u).catch(function(d){var w,A;if(!i.isResolved){var S=(w=a.retry)!=null?w:3,b=(A=a.retryDelay)!=null?A:na,R=typeof b=="function"?b(i.failureCount,d):b,V=S===!0||typeof S=="number"&&i.failureCount<S||typeof S=="function"&&S(i.failureCount,d);if(r||!V){h(d);return}i.failureCount++,a.onFail==null||a.onFail(i.failureCount,d),ta(R).then(function(){if(!Xe.isFocused()||!lt.isOnline())return m()}).then(function(){r?h(d):N()})}})}};f()},ia=function(){function t(){this.queue=[],this.transactions=0,this.notifyFn=function(i){i()},this.batchNotifyFn=function(i){i()}}var a=t.prototype;return a.batch=function(r){var n;this.transactions++;try{n=r()}finally{this.transactions--,this.transactions||this.flush()}return n},a.schedule=function(r){var n=this;this.transactions?this.queue.push(r):Jt(function(){n.notifyFn(r)})},a.batchCalls=function(r){var n=this;return function(){for(var s=arguments.length,l=new Array(s),o=0;o<s;o++)l[o]=arguments[o];n.schedule(function(){r.apply(void 0,l)})}},a.flush=function(){var r=this,n=this.queue;this.queue=[],n.length&&Jt(function(){r.batchNotifyFn(function(){n.forEach(function(s){r.notifyFn(s)})})})},a.setNotifyFunction=function(r){this.notifyFn=r},a.setBatchNotifyFunction=function(r){this.batchNotifyFn=r},t}(),me=new ia,br=console;function yt(){return br}function sa(t){br=t}var la=function(){function t(i){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=i.defaultOptions,this.setOptions(i.options),this.observers=[],this.cache=i.cache,this.queryKey=i.queryKey,this.queryHash=i.queryHash,this.initialState=i.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=i.meta,this.scheduleGc()}var a=t.prototype;return a.setOptions=function(r){var n;this.options=J({},this.defaultOptions,r),this.meta=r==null?void 0:r.meta,this.cacheTime=Math.max(this.cacheTime||0,(n=this.options.cacheTime)!=null?n:5*60*1e3)},a.setDefaultOptions=function(r){this.defaultOptions=r},a.scheduleGc=function(){var r=this;this.clearGcTimeout(),xt(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){r.optionalRemove()},this.cacheTime))},a.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},a.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},a.setData=function(r,n){var s,l,o=this.state.data,u=Xr(r,o);return(s=(l=this.options).isDataEqual)!=null&&s.call(l,o,u)?u=o:this.options.structuralSharing!==!1&&(u=ft(o,u)),this.dispatch({data:u,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt}),u},a.setState=function(r,n){this.dispatch({type:"setState",state:r,setStateOptions:n})},a.cancel=function(r){var n,s=this.promise;return(n=this.retryer)==null||n.cancel(r),s?s.then(ke).catch(ke):Promise.resolve()},a.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},a.reset=function(){this.destroy(),this.setState(this.initialState)},a.isActive=function(){return this.observers.some(function(r){return r.options.enabled!==!1})},a.isFetching=function(){return this.state.isFetching},a.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(r){return r.getCurrentResult().isStale})},a.isStaleByTime=function(r){return r===void 0&&(r=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!fr(this.state.dataUpdatedAt,r)},a.onFocus=function(){var r,n=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});n&&n.refetch(),(r=this.retryer)==null||r.continue()},a.onOnline=function(){var r,n=this.observers.find(function(s){return s.shouldFetchOnReconnect()});n&&n.refetch(),(r=this.retryer)==null||r.continue()},a.addObserver=function(r){this.observers.indexOf(r)===-1&&(this.observers.push(r),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:r}))},a.removeObserver=function(r){this.observers.indexOf(r)!==-1&&(this.observers=this.observers.filter(function(n){return n!==r}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:r}))},a.getObserversCount=function(){return this.observers.length},a.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},a.fetch=function(r,n){var s=this,l,o,u;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var h;return(h=this.retryer)==null||h.continueRetry(),this.promise}}if(r&&this.setOptions(r),!this.options.queryFn){var m=this.observers.find(function(b){return b.options.queryFn});m&&this.setOptions(m.options)}var f=ht(this.queryKey),N=yr(),v={queryKey:f,pageParam:void 0,meta:this.meta};Object.defineProperty(v,"signal",{enumerable:!0,get:function(){if(N)return s.abortSignalConsumed=!0,N.signal}});var d=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(v)):Promise.reject("Missing queryFn")},w={fetchOptions:n,options:this.options,queryKey:f,state:this.state,fetchFn:d,meta:this.meta};if((l=this.options.behavior)!=null&&l.onFetch){var A;(A=this.options.behavior)==null||A.onFetch(w)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((o=w.fetchOptions)==null?void 0:o.meta)){var S;this.dispatch({type:"fetch",meta:(S=w.fetchOptions)==null?void 0:S.meta})}return this.retryer=new vr({fn:w.fetchFn,abort:N==null||(u=N.abort)==null?void 0:u.bind(N),onSuccess:function(R){s.setData(R),s.cache.config.onSuccess==null||s.cache.config.onSuccess(R,s),s.cacheTime===0&&s.optionalRemove()},onError:function(R){ot(R)&&R.silent||s.dispatch({type:"error",error:R}),ot(R)||(s.cache.config.onError==null||s.cache.config.onError(R,s),yt().error(R)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:w.options.retry,retryDelay:w.options.retryDelay}),this.promise=this.retryer.promise,this.promise},a.dispatch=function(r){var n=this;this.state=this.reducer(this.state,r),me.batch(function(){n.observers.forEach(function(s){s.onQueryUpdate(r)}),n.cache.notify({query:n,type:"queryUpdated",action:r})})},a.getDefaultState=function(r){var n=typeof r.initialData=="function"?r.initialData():r.initialData,s=typeof r.initialData<"u",l=s?typeof r.initialDataUpdatedAt=="function"?r.initialDataUpdatedAt():r.initialDataUpdatedAt:0,o=typeof n<"u";return{data:n,dataUpdateCount:0,dataUpdatedAt:o?l??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:o?"success":"idle"}},a.reducer=function(r,n){var s,l;switch(n.type){case"failed":return J({},r,{fetchFailureCount:r.fetchFailureCount+1});case"pause":return J({},r,{isPaused:!0});case"continue":return J({},r,{isPaused:!1});case"fetch":return J({},r,{fetchFailureCount:0,fetchMeta:(s=n.meta)!=null?s:null,isFetching:!0,isPaused:!1},!r.dataUpdatedAt&&{error:null,status:"loading"});case"success":return J({},r,{data:n.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:(l=n.dataUpdatedAt)!=null?l:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=n.error;return ot(o)&&o.revert&&this.revertState?J({},this.revertState):J({},r,{error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return J({},r,{isInvalidated:!0});case"setState":return J({},r,n.state);default:return r}},t}(),oa=function(t){Je(a,t);function a(r){var n;return n=t.call(this)||this,n.config=r||{},n.queries=[],n.queriesMap={},n}var i=a.prototype;return i.build=function(n,s,l){var o,u=s.queryKey,h=(o=s.queryHash)!=null?o:Ht(u,s),m=this.get(h);return m||(m=new la({cache:this,queryKey:u,queryHash:h,options:n.defaultQueryOptions(s),state:l,defaultOptions:n.getQueryDefaults(u),meta:s.meta}),this.add(m)),m},i.add=function(n){this.queriesMap[n.queryHash]||(this.queriesMap[n.queryHash]=n,this.queries.push(n),this.notify({type:"queryAdded",query:n}))},i.remove=function(n){var s=this.queriesMap[n.queryHash];s&&(n.destroy(),this.queries=this.queries.filter(function(l){return l!==n}),s===n&&delete this.queriesMap[n.queryHash],this.notify({type:"queryRemoved",query:n}))},i.clear=function(){var n=this;me.batch(function(){n.queries.forEach(function(s){n.remove(s)})})},i.get=function(n){return this.queriesMap[n]},i.getAll=function(){return this.queries},i.find=function(n,s){var l=je(n,s),o=l[0];return typeof o.exact>"u"&&(o.exact=!0),this.queries.find(function(u){return Gt(o,u)})},i.findAll=function(n,s){var l=je(n,s),o=l[0];return Object.keys(o).length>0?this.queries.filter(function(u){return Gt(o,u)}):this.queries},i.notify=function(n){var s=this;me.batch(function(){s.listeners.forEach(function(l){l(n)})})},i.onFocus=function(){var n=this;me.batch(function(){n.queries.forEach(function(s){s.onFocus()})})},i.onOnline=function(){var n=this;me.batch(function(){n.queries.forEach(function(s){s.onOnline()})})},a}(Ze),ca=function(){function t(i){this.options=J({},i.defaultOptions,i.options),this.mutationId=i.mutationId,this.mutationCache=i.mutationCache,this.observers=[],this.state=i.state||ua(),this.meta=i.meta}var a=t.prototype;return a.setState=function(r){this.dispatch({type:"setState",state:r})},a.addObserver=function(r){this.observers.indexOf(r)===-1&&this.observers.push(r)},a.removeObserver=function(r){this.observers=this.observers.filter(function(n){return n!==r})},a.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(ke).catch(ke)):Promise.resolve()},a.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},a.execute=function(){var r=this,n,s=this.state.status==="loading",l=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),l=l.then(function(){r.mutationCache.config.onMutate==null||r.mutationCache.config.onMutate(r.state.variables,r)}).then(function(){return r.options.onMutate==null?void 0:r.options.onMutate(r.state.variables)}).then(function(o){o!==r.state.context&&r.dispatch({type:"loading",context:o,variables:r.state.variables})})),l.then(function(){return r.executeMutation()}).then(function(o){n=o,r.mutationCache.config.onSuccess==null||r.mutationCache.config.onSuccess(n,r.state.variables,r.state.context,r)}).then(function(){return r.options.onSuccess==null?void 0:r.options.onSuccess(n,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(n,null,r.state.variables,r.state.context)}).then(function(){return r.dispatch({type:"success",data:n}),n}).catch(function(o){return r.mutationCache.config.onError==null||r.mutationCache.config.onError(o,r.state.variables,r.state.context,r),yt().error(o),Promise.resolve().then(function(){return r.options.onError==null?void 0:r.options.onError(o,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(void 0,o,r.state.variables,r.state.context)}).then(function(){throw r.dispatch({type:"error",error:o}),o})})},a.executeMutation=function(){var r=this,n;return this.retryer=new vr({fn:function(){return r.options.mutationFn?r.options.mutationFn(r.state.variables):Promise.reject("No mutationFn found")},onFail:function(){r.dispatch({type:"failed"})},onPause:function(){r.dispatch({type:"pause"})},onContinue:function(){r.dispatch({type:"continue"})},retry:(n=this.options.retry)!=null?n:0,retryDelay:this.options.retryDelay}),this.retryer.promise},a.dispatch=function(r){var n=this;this.state=da(this.state,r),me.batch(function(){n.observers.forEach(function(s){s.onMutationUpdate(r)}),n.mutationCache.notify(n)})},t}();function ua(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function da(t,a){switch(a.type){case"failed":return J({},t,{failureCount:t.failureCount+1});case"pause":return J({},t,{isPaused:!0});case"continue":return J({},t,{isPaused:!1});case"loading":return J({},t,{context:a.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:a.variables});case"success":return J({},t,{data:a.data,error:null,status:"success",isPaused:!1});case"error":return J({},t,{data:void 0,error:a.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return J({},t,a.state);default:return t}}var ha=function(t){Je(a,t);function a(r){var n;return n=t.call(this)||this,n.config=r||{},n.mutations=[],n.mutationId=0,n}var i=a.prototype;return i.build=function(n,s,l){var o=new ca({mutationCache:this,mutationId:++this.mutationId,options:n.defaultMutationOptions(s),state:l,defaultOptions:s.mutationKey?n.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(o),o},i.add=function(n){this.mutations.push(n),this.notify(n)},i.remove=function(n){this.mutations=this.mutations.filter(function(s){return s!==n}),n.cancel(),this.notify(n)},i.clear=function(){var n=this;me.batch(function(){n.mutations.forEach(function(s){n.remove(s)})})},i.getAll=function(){return this.mutations},i.find=function(n){return typeof n.exact>"u"&&(n.exact=!0),this.mutations.find(function(s){return Wt(n,s)})},i.findAll=function(n){return this.mutations.filter(function(s){return Wt(n,s)})},i.notify=function(n){var s=this;me.batch(function(){s.listeners.forEach(function(l){l(n)})})},i.onFocus=function(){this.resumePausedMutations()},i.onOnline=function(){this.resumePausedMutations()},i.resumePausedMutations=function(){var n=this.mutations.filter(function(s){return s.state.isPaused});return me.batch(function(){return n.reduce(function(s,l){return s.then(function(){return l.continue().catch(ke)})},Promise.resolve())})},a}(Ze);function ma(){return{onFetch:function(a){a.fetchFn=function(){var i,r,n,s,l,o,u=(i=a.fetchOptions)==null||(r=i.meta)==null?void 0:r.refetchPage,h=(n=a.fetchOptions)==null||(s=n.meta)==null?void 0:s.fetchMore,m=h==null?void 0:h.pageParam,f=(h==null?void 0:h.direction)==="forward",N=(h==null?void 0:h.direction)==="backward",v=((l=a.state.data)==null?void 0:l.pages)||[],d=((o=a.state.data)==null?void 0:o.pageParams)||[],w=yr(),A=w==null?void 0:w.signal,S=d,b=!1,R=a.options.queryFn||function(){return Promise.reject("Missing queryFn")},V=function(y,C,E,U){return S=U?[C].concat(S):[].concat(S,[C]),U?[E].concat(y):[].concat(y,[E])},j=function(y,C,E,U){if(b)return Promise.reject("Cancelled");if(typeof E>"u"&&!C&&y.length)return Promise.resolve(y);var B={queryKey:a.queryKey,signal:A,pageParam:E,meta:a.meta},Z=R(B),q=Promise.resolve(Z).then(function(ne){return V(y,E,ne,U)});if(pt(Z)){var ae=q;ae.cancel=Z.cancel}return q},D;if(!v.length)D=j([]);else if(f){var le=typeof m<"u",H=le?m:Zt(a.options,v);D=j(v,le,H)}else if(N){var Y=typeof m<"u",W=Y?m:fa(a.options,v);D=j(v,Y,W,!0)}else(function(){S=[];var X=typeof a.options.getNextPageParam>"u",y=u&&v[0]?u(v[0],0,v):!0;D=y?j([],X,d[0]):Promise.resolve(V([],d[0],v[0]));for(var C=function(B){D=D.then(function(Z){var q=u&&v[B]?u(v[B],B,v):!0;if(q){var ae=X?d[B]:Zt(a.options,Z);return j(Z,X,ae)}return Promise.resolve(V(Z,d[B],v[B]))})},E=1;E<v.length;E++)C(E)})();var te=D.then(function(X){return{pages:X,pageParams:S}}),G=te;return G.cancel=function(){b=!0,w==null||w.abort(),pt(D)&&D.cancel()},te}}}}function Zt(t,a){return t.getNextPageParam==null?void 0:t.getNextPageParam(a[a.length-1],a)}function fa(t,a){return t.getPreviousPageParam==null?void 0:t.getPreviousPageParam(a[0],a)}var pa=function(){function t(i){i===void 0&&(i={}),this.queryCache=i.queryCache||new oa,this.mutationCache=i.mutationCache||new ha,this.defaultOptions=i.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var a=t.prototype;return a.mount=function(){var r=this;this.unsubscribeFocus=Xe.subscribe(function(){Xe.isFocused()&&lt.isOnline()&&(r.mutationCache.onFocus(),r.queryCache.onFocus())}),this.unsubscribeOnline=lt.subscribe(function(){Xe.isFocused()&&lt.isOnline()&&(r.mutationCache.onOnline(),r.queryCache.onOnline())})},a.unmount=function(){var r,n;(r=this.unsubscribeFocus)==null||r.call(this),(n=this.unsubscribeOnline)==null||n.call(this)},a.isFetching=function(r,n){var s=je(r,n),l=s[0];return l.fetching=!0,this.queryCache.findAll(l).length},a.isMutating=function(r){return this.mutationCache.findAll(J({},r,{fetching:!0})).length},a.getQueryData=function(r,n){var s;return(s=this.queryCache.find(r,n))==null?void 0:s.state.data},a.getQueriesData=function(r){return this.getQueryCache().findAll(r).map(function(n){var s=n.queryKey,l=n.state,o=l.data;return[s,o]})},a.setQueryData=function(r,n,s){var l=st(r),o=this.defaultQueryOptions(l);return this.queryCache.build(this,o).setData(n,s)},a.setQueriesData=function(r,n,s){var l=this;return me.batch(function(){return l.getQueryCache().findAll(r).map(function(o){var u=o.queryKey;return[u,l.setQueryData(u,n,s)]})})},a.getQueryState=function(r,n){var s;return(s=this.queryCache.find(r,n))==null?void 0:s.state},a.removeQueries=function(r,n){var s=je(r,n),l=s[0],o=this.queryCache;me.batch(function(){o.findAll(l).forEach(function(u){o.remove(u)})})},a.resetQueries=function(r,n,s){var l=this,o=je(r,n,s),u=o[0],h=o[1],m=this.queryCache,f=J({},u,{active:!0});return me.batch(function(){return m.findAll(u).forEach(function(N){N.reset()}),l.refetchQueries(f,h)})},a.cancelQueries=function(r,n,s){var l=this,o=je(r,n,s),u=o[0],h=o[1],m=h===void 0?{}:h;typeof m.revert>"u"&&(m.revert=!0);var f=me.batch(function(){return l.queryCache.findAll(u).map(function(N){return N.cancel(m)})});return Promise.all(f).then(ke).catch(ke)},a.invalidateQueries=function(r,n,s){var l,o,u,h=this,m=je(r,n,s),f=m[0],N=m[1],v=J({},f,{active:(l=(o=f.refetchActive)!=null?o:f.active)!=null?l:!0,inactive:(u=f.refetchInactive)!=null?u:!1});return me.batch(function(){return h.queryCache.findAll(f).forEach(function(d){d.invalidate()}),h.refetchQueries(v,N)})},a.refetchQueries=function(r,n,s){var l=this,o=je(r,n,s),u=o[0],h=o[1],m=me.batch(function(){return l.queryCache.findAll(u).map(function(N){return N.fetch(void 0,J({},h,{meta:{refetchPage:u==null?void 0:u.refetchPage}}))})}),f=Promise.all(m).then(ke);return h!=null&&h.throwOnError||(f=f.catch(ke)),f},a.fetchQuery=function(r,n,s){var l=st(r,n,s),o=this.defaultQueryOptions(l);typeof o.retry>"u"&&(o.retry=!1);var u=this.queryCache.build(this,o);return u.isStaleByTime(o.staleTime)?u.fetch(o):Promise.resolve(u.state.data)},a.prefetchQuery=function(r,n,s){return this.fetchQuery(r,n,s).then(ke).catch(ke)},a.fetchInfiniteQuery=function(r,n,s){var l=st(r,n,s);return l.behavior=ma(),this.fetchQuery(l)},a.prefetchInfiniteQuery=function(r,n,s){return this.fetchInfiniteQuery(r,n,s).then(ke).catch(ke)},a.cancelMutations=function(){var r=this,n=me.batch(function(){return r.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(n).then(ke).catch(ke)},a.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},a.executeMutation=function(r){return this.mutationCache.build(this,r).execute()},a.getQueryCache=function(){return this.queryCache},a.getMutationCache=function(){return this.mutationCache},a.getDefaultOptions=function(){return this.defaultOptions},a.setDefaultOptions=function(r){this.defaultOptions=r},a.setQueryDefaults=function(r,n){var s=this.queryDefaults.find(function(l){return He(r)===He(l.queryKey)});s?s.defaultOptions=n:this.queryDefaults.push({queryKey:r,defaultOptions:n})},a.getQueryDefaults=function(r){var n;return r?(n=this.queryDefaults.find(function(s){return mt(r,s.queryKey)}))==null?void 0:n.defaultOptions:void 0},a.setMutationDefaults=function(r,n){var s=this.mutationDefaults.find(function(l){return He(r)===He(l.mutationKey)});s?s.defaultOptions=n:this.mutationDefaults.push({mutationKey:r,defaultOptions:n})},a.getMutationDefaults=function(r){var n;return r?(n=this.mutationDefaults.find(function(s){return mt(r,s.mutationKey)}))==null?void 0:n.defaultOptions:void 0},a.defaultQueryOptions=function(r){if(r!=null&&r._defaulted)return r;var n=J({},this.defaultOptions.queries,this.getQueryDefaults(r==null?void 0:r.queryKey),r,{_defaulted:!0});return!n.queryHash&&n.queryKey&&(n.queryHash=Ht(n.queryKey,n)),n},a.defaultQueryObserverOptions=function(r){return this.defaultQueryOptions(r)},a.defaultMutationOptions=function(r){return r!=null&&r._defaulted?r:J({},this.defaultOptions.mutations,this.getMutationDefaults(r==null?void 0:r.mutationKey),r,{_defaulted:!0})},a.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},t}(),ya=function(t){Je(a,t);function a(r,n){var s;return s=t.call(this)||this,s.client=r,s.options=n,s.trackedProps=[],s.selectError=null,s.bindMethods(),s.setOptions(n),s}var i=a.prototype;return i.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},i.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),er(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},i.onUnsubscribe=function(){this.listeners.length||this.destroy()},i.shouldFetchOnReconnect=function(){return Mt(this.currentQuery,this.options,this.options.refetchOnReconnect)},i.shouldFetchOnWindowFocus=function(){return Mt(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},i.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},i.setOptions=function(n,s){var l=this.options,o=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(n),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=l.queryKey),this.updateQuery();var u=this.hasListeners();u&&tr(this.currentQuery,o,this.options,l)&&this.executeFetch(),this.updateResult(s),u&&(this.currentQuery!==o||this.options.enabled!==l.enabled||this.options.staleTime!==l.staleTime)&&this.updateStaleTimeout();var h=this.computeRefetchInterval();u&&(this.currentQuery!==o||this.options.enabled!==l.enabled||h!==this.currentRefetchInterval)&&this.updateRefetchInterval(h)},i.getOptimisticResult=function(n){var s=this.client.defaultQueryObserverOptions(n),l=this.client.getQueryCache().build(this.client,s);return this.createResult(l,s)},i.getCurrentResult=function(){return this.currentResult},i.trackResult=function(n,s){var l=this,o={},u=function(m){l.trackedProps.includes(m)||l.trackedProps.push(m)};return Object.keys(n).forEach(function(h){Object.defineProperty(o,h,{configurable:!1,enumerable:!0,get:function(){return u(h),n[h]}})}),(s.useErrorBoundary||s.suspense)&&u("error"),o},i.getNextResult=function(n){var s=this;return new Promise(function(l,o){var u=s.subscribe(function(h){h.isFetching||(u(),h.isError&&(n!=null&&n.throwOnError)?o(h.error):l(h))})})},i.getCurrentQuery=function(){return this.currentQuery},i.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},i.refetch=function(n){return this.fetch(J({},n,{meta:{refetchPage:n==null?void 0:n.refetchPage}}))},i.fetchOptimistic=function(n){var s=this,l=this.client.defaultQueryObserverOptions(n),o=this.client.getQueryCache().build(this.client,l);return o.fetch().then(function(){return s.createResult(o,l)})},i.fetch=function(n){var s=this;return this.executeFetch(n).then(function(){return s.updateResult(),s.currentResult})},i.executeFetch=function(n){this.updateQuery();var s=this.currentQuery.fetch(this.options,n);return n!=null&&n.throwOnError||(s=s.catch(ke)),s},i.updateStaleTimeout=function(){var n=this;if(this.clearStaleTimeout(),!(dt||this.currentResult.isStale||!xt(this.options.staleTime))){var s=fr(this.currentResult.dataUpdatedAt,this.options.staleTime),l=s+1;this.staleTimeoutId=setTimeout(function(){n.currentResult.isStale||n.updateResult()},l)}},i.computeRefetchInterval=function(){var n;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(n=this.options.refetchInterval)!=null?n:!1},i.updateRefetchInterval=function(n){var s=this;this.clearRefetchInterval(),this.currentRefetchInterval=n,!(dt||this.options.enabled===!1||!xt(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(s.options.refetchIntervalInBackground||Xe.isFocused())&&s.executeFetch()},this.currentRefetchInterval))},i.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},i.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},i.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},i.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},i.createResult=function(n,s){var l=this.currentQuery,o=this.options,u=this.currentResult,h=this.currentResultState,m=this.currentResultOptions,f=n!==l,N=f?n.state:this.currentQueryInitialState,v=f?this.currentResult:this.previousQueryResult,d=n.state,w=d.dataUpdatedAt,A=d.error,S=d.errorUpdatedAt,b=d.isFetching,R=d.status,V=!1,j=!1,D;if(s.optimisticResults){var le=this.hasListeners(),H=!le&&er(n,s),Y=le&&tr(n,l,s,o);(H||Y)&&(b=!0,w||(R="loading"))}if(s.keepPreviousData&&!d.dataUpdateCount&&(v!=null&&v.isSuccess)&&R!=="error")D=v.data,w=v.dataUpdatedAt,R=v.status,V=!0;else if(s.select&&typeof d.data<"u")if(u&&d.data===(h==null?void 0:h.data)&&s.select===this.selectFn)D=this.selectResult;else try{this.selectFn=s.select,D=s.select(d.data),s.structuralSharing!==!1&&(D=ft(u==null?void 0:u.data,D)),this.selectResult=D,this.selectError=null}catch(G){yt().error(G),this.selectError=G}else D=d.data;if(typeof s.placeholderData<"u"&&typeof D>"u"&&(R==="loading"||R==="idle")){var W;if(u!=null&&u.isPlaceholderData&&s.placeholderData===(m==null?void 0:m.placeholderData))W=u.data;else if(W=typeof s.placeholderData=="function"?s.placeholderData():s.placeholderData,s.select&&typeof W<"u")try{W=s.select(W),s.structuralSharing!==!1&&(W=ft(u==null?void 0:u.data,W)),this.selectError=null}catch(G){yt().error(G),this.selectError=G}typeof W<"u"&&(R="success",D=W,j=!0)}this.selectError&&(A=this.selectError,D=this.selectResult,S=Date.now(),R="error");var te={status:R,isLoading:R==="loading",isSuccess:R==="success",isError:R==="error",isIdle:R==="idle",data:D,dataUpdatedAt:w,error:A,errorUpdatedAt:S,failureCount:d.fetchFailureCount,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>N.dataUpdateCount||d.errorUpdateCount>N.errorUpdateCount,isFetching:b,isRefetching:b&&R!=="loading",isLoadingError:R==="error"&&d.dataUpdatedAt===0,isPlaceholderData:j,isPreviousData:V,isRefetchError:R==="error"&&d.dataUpdatedAt!==0,isStale:Ut(n,s),refetch:this.refetch,remove:this.remove};return te},i.shouldNotifyListeners=function(n,s){if(!s)return!0;var l=this.options,o=l.notifyOnChangeProps,u=l.notifyOnChangePropsExclusions;if(!o&&!u||o==="tracked"&&!this.trackedProps.length)return!0;var h=o==="tracked"?this.trackedProps:o;return Object.keys(n).some(function(m){var f=m,N=n[f]!==s[f],v=h==null?void 0:h.some(function(w){return w===m}),d=u==null?void 0:u.some(function(w){return w===m});return N&&!d&&(!h||v)})},i.updateResult=function(n){var s=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!ea(this.currentResult,s)){var l={cache:!0};(n==null?void 0:n.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,s)&&(l.listeners=!0),this.notify(J({},l,n))}},i.updateQuery=function(){var n=this.client.getQueryCache().build(this.client,this.options);if(n!==this.currentQuery){var s=this.currentQuery;this.currentQuery=n,this.currentQueryInitialState=n.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(s==null||s.removeObserver(this),n.addObserver(this))}},i.onQueryUpdate=function(n){var s={};n.type==="success"?s.onSuccess=!0:n.type==="error"&&!ot(n.error)&&(s.onError=!0),this.updateResult(s),this.hasListeners()&&this.updateTimers()},i.notify=function(n){var s=this;me.batch(function(){n.onSuccess?(s.options.onSuccess==null||s.options.onSuccess(s.currentResult.data),s.options.onSettled==null||s.options.onSettled(s.currentResult.data,null)):n.onError&&(s.options.onError==null||s.options.onError(s.currentResult.error),s.options.onSettled==null||s.options.onSettled(void 0,s.currentResult.error)),n.listeners&&s.listeners.forEach(function(l){l(s.currentResult)}),n.cache&&s.client.getQueryCache().notify({query:s.currentQuery,type:"observerResultsUpdated"})})},a}(Ze);function ga(t,a){return a.enabled!==!1&&!t.state.dataUpdatedAt&&!(t.state.status==="error"&&a.retryOnMount===!1)}function er(t,a){return ga(t,a)||t.state.dataUpdatedAt>0&&Mt(t,a,a.refetchOnMount)}function Mt(t,a,i){if(a.enabled!==!1){var r=typeof i=="function"?i(t):i;return r==="always"||r!==!1&&Ut(t,a)}return!1}function tr(t,a,i,r){return i.enabled!==!1&&(t!==a||r.enabled===!1)&&(!i.suspense||t.state.status!=="error")&&Ut(t,i)}function Ut(t,a){return t.isStaleByTime(a.staleTime)}var va=Fr.unstable_batchedUpdates;me.setBatchNotifyFunction(va);var ba=console;sa(ba);var rr=oe.createContext(void 0),Nr=oe.createContext(!1);function wr(t){return t&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=rr),window.ReactQueryClientContext):rr}var _t=function(){var a=oe.useContext(wr(oe.useContext(Nr)));if(!a)throw new Error("No QueryClient set, use QueryClientProvider to set one");return a},Na=function(a){var i=a.client,r=a.contextSharing,n=r===void 0?!1:r,s=a.children;oe.useEffect(function(){return i.mount(),function(){i.unmount()}},[i]);var l=wr(n);return oe.createElement(Nr.Provider,{value:n},oe.createElement(l.Provider,{value:i},s))};function wa(){var t=!1;return{clearReset:function(){t=!1},reset:function(){t=!0},isReset:function(){return t}}}var ka=oe.createContext(wa()),_a=function(){return oe.useContext(ka)};function Ta(t,a,i){return typeof a=="function"?a.apply(void 0,i):typeof a=="boolean"?a:!!t}function Sa(t,a){var i=oe.useRef(!1),r=oe.useState(0),n=r[1],s=_t(),l=_a(),o=s.defaultQueryObserverOptions(t);o.optimisticResults=!0,o.onError&&(o.onError=me.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=me.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=me.batchCalls(o.onSettled)),o.suspense&&(typeof o.staleTime!="number"&&(o.staleTime=1e3),o.cacheTime===0&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(l.isReset()||(o.retryOnMount=!1));var u=oe.useState(function(){return new a(s,o)}),h=u[0],m=h.getOptimisticResult(o);if(oe.useEffect(function(){i.current=!0,l.clearReset();var f=h.subscribe(me.batchCalls(function(){i.current&&n(function(N){return N+1})}));return h.updateResult(),function(){i.current=!1,f()}},[l,h]),oe.useEffect(function(){h.setOptions(o,{listeners:!1})},[o,h]),o.suspense&&m.isLoading)throw h.fetchOptimistic(o).then(function(f){var N=f.data;o.onSuccess==null||o.onSuccess(N),o.onSettled==null||o.onSettled(N,null)}).catch(function(f){l.clearReset(),o.onError==null||o.onError(f),o.onSettled==null||o.onSettled(void 0,f)});if(m.isError&&!l.isReset()&&!m.isFetching&&Ta(o.suspense,o.useErrorBoundary,[m.error,h.getCurrentQuery()]))throw m.error;return o.notifyOnChangeProps==="tracked"&&(m=h.trackResult(m,o)),m}function ye(t,a,i){var r=st(t,a,i);return Sa(r,ya)}var kr={exports:{}},Oa="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Ca=Oa,Ea=Ca;function _r(){}function Tr(){}Tr.resetWarningCache=_r;var Pa=function(){function t(r,n,s,l,o,u){if(u!==Ea){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}t.isRequired=t;function a(){return t}var i={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:a,element:t,elementType:t,instanceOf:a,node:t,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:Tr,resetWarningCache:_r};return i.PropTypes=i,i};kr.exports=Pa();var Aa=kr.exports;const M=Nt(Aa);var Ra=typeof Element<"u",xa=typeof Map=="function",La=typeof Set=="function",Ma=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function ct(t,a){if(t===a)return!0;if(t&&a&&typeof t=="object"&&typeof a=="object"){if(t.constructor!==a.constructor)return!1;var i,r,n;if(Array.isArray(t)){if(i=t.length,i!=a.length)return!1;for(r=i;r--!==0;)if(!ct(t[r],a[r]))return!1;return!0}var s;if(xa&&t instanceof Map&&a instanceof Map){if(t.size!==a.size)return!1;for(s=t.entries();!(r=s.next()).done;)if(!a.has(r.value[0]))return!1;for(s=t.entries();!(r=s.next()).done;)if(!ct(r.value[1],a.get(r.value[0])))return!1;return!0}if(La&&t instanceof Set&&a instanceof Set){if(t.size!==a.size)return!1;for(s=t.entries();!(r=s.next()).done;)if(!a.has(r.value[0]))return!1;return!0}if(Ma&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(a)){if(i=t.length,i!=a.length)return!1;for(r=i;r--!==0;)if(t[r]!==a[r])return!1;return!0}if(t.constructor===RegExp)return t.source===a.source&&t.flags===a.flags;if(t.valueOf!==Object.prototype.valueOf&&typeof t.valueOf=="function"&&typeof a.valueOf=="function")return t.valueOf()===a.valueOf();if(t.toString!==Object.prototype.toString&&typeof t.toString=="function"&&typeof a.toString=="function")return t.toString()===a.toString();if(n=Object.keys(t),i=n.length,i!==Object.keys(a).length)return!1;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(a,n[r]))return!1;if(Ra&&t instanceof Element)return!1;for(r=i;r--!==0;)if(!((n[r]==="_owner"||n[r]==="__v"||n[r]==="__o")&&t.$$typeof)&&!ct(t[n[r]],a[n[r]]))return!1;return!0}return t!==t&&a!==a}var Ia=function(a,i){try{return ct(a,i)}catch(r){if((r.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw r}};const Da=Nt(Ia);var Fa=function(t,a,i,r,n,s,l,o){if(!t){var u;if(a===void 0)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var h=[i,r,n,s,l,o],m=0;u=new Error(a.replace(/%s/g,function(){return h[m++]})),u.name="Invariant Violation"}throw u.framesToPop=1,u}},ja=Fa;const ar=Nt(ja);var za=function(a,i,r,n){var s=r?r.call(n,a,i):void 0;if(s!==void 0)return!!s;if(a===i)return!0;if(typeof a!="object"||!a||typeof i!="object"||!i)return!1;var l=Object.keys(a),o=Object.keys(i);if(l.length!==o.length)return!1;for(var u=Object.prototype.hasOwnProperty.bind(i),h=0;h<l.length;h++){var m=l[h];if(!u(m))return!1;var f=a[m],N=i[m];if(s=r?r.call(n,f,N,m):void 0,s===!1||s===void 0&&f!==N)return!1}return!0};const Ha=Nt(za);function pe(){return pe=Object.assign||function(t){for(var a=1;a<arguments.length;a++){var i=arguments[a];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},pe.apply(this,arguments)}function Bt(t,a){t.prototype=Object.create(a.prototype),t.prototype.constructor=t,It(t,a)}function It(t,a){return It=Object.setPrototypeOf||function(i,r){return i.__proto__=r,i},It(t,a)}function nr(t,a){if(t==null)return{};var i,r,n={},s=Object.keys(t);for(r=0;r<s.length;r++)a.indexOf(i=s[r])>=0||(n[i]=t[i]);return n}var F={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title",FRAGMENT:"Symbol(react.fragment)"},Ua={rel:["amphtml","canonical","alternate"]},Ba={type:["application/ld+json"]},$a={charset:"",name:["robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},ir=Object.keys(F).map(function(t){return F[t]}),gt={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},qa=Object.keys(gt).reduce(function(t,a){return t[gt[a]]=a,t},{}),Ke=function(t,a){for(var i=t.length-1;i>=0;i-=1){var r=t[i];if(Object.prototype.hasOwnProperty.call(r,a))return r[a]}return null},Qa=function(t){var a=Ke(t,F.TITLE),i=Ke(t,"titleTemplate");if(Array.isArray(a)&&(a=a.join("")),i&&a)return i.replace(/%s/g,function(){return a});var r=Ke(t,"defaultTitle");return a||r||void 0},Ka=function(t){return Ke(t,"onChangeClientState")||function(){}},Ct=function(t,a){return a.filter(function(i){return i[t]!==void 0}).map(function(i){return i[t]}).reduce(function(i,r){return pe({},i,r)},{})},Va=function(t,a){return a.filter(function(i){return i[F.BASE]!==void 0}).map(function(i){return i[F.BASE]}).reverse().reduce(function(i,r){if(!i.length)for(var n=Object.keys(r),s=0;s<n.length;s+=1){var l=n[s].toLowerCase();if(t.indexOf(l)!==-1&&r[l])return i.concat(r)}return i},[])},Ye=function(t,a,i){var r={};return i.filter(function(n){return!!Array.isArray(n[t])||(n[t]!==void 0&&console&&typeof console.warn=="function"&&console.warn("Helmet: "+t+' should be of type "Array". Instead found type "'+typeof n[t]+'"'),!1)}).map(function(n){return n[t]}).reverse().reduce(function(n,s){var l={};s.filter(function(f){for(var N,v=Object.keys(f),d=0;d<v.length;d+=1){var w=v[d],A=w.toLowerCase();a.indexOf(A)===-1||N==="rel"&&f[N].toLowerCase()==="canonical"||A==="rel"&&f[A].toLowerCase()==="stylesheet"||(N=A),a.indexOf(w)===-1||w!=="innerHTML"&&w!=="cssText"&&w!=="itemprop"||(N=w)}if(!N||!f[N])return!1;var S=f[N].toLowerCase();return r[N]||(r[N]={}),l[N]||(l[N]={}),!r[N][S]&&(l[N][S]=!0,!0)}).reverse().forEach(function(f){return n.push(f)});for(var o=Object.keys(l),u=0;u<o.length;u+=1){var h=o[u],m=pe({},r[h],l[h]);r[h]=m}return n},[]).reverse()},Ya=function(t,a){if(Array.isArray(t)&&t.length){for(var i=0;i<t.length;i+=1)if(t[i][a])return!0}return!1},Sr=function(t){return Array.isArray(t)?t.join(""):t},Et=function(t,a){return Array.isArray(t)?t.reduce(function(i,r){return function(n,s){for(var l=Object.keys(n),o=0;o<l.length;o+=1)if(s[l[o]]&&s[l[o]].includes(n[l[o]]))return!0;return!1}(r,a)?i.priority.push(r):i.default.push(r),i},{priority:[],default:[]}):{default:t}},sr=function(t,a){var i;return pe({},t,((i={})[a]=void 0,i))},Ga=[F.NOSCRIPT,F.SCRIPT,F.STYLE],Pt=function(t,a){return a===void 0&&(a=!0),a===!1?String(t):String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},lr=function(t){return Object.keys(t).reduce(function(a,i){var r=t[i]!==void 0?i+'="'+t[i]+'"':""+i;return a?a+" "+r:r},"")},or=function(t,a){return a===void 0&&(a={}),Object.keys(t).reduce(function(i,r){return i[gt[r]||r]=t[r],i},a)},ut=function(t,a){return a.map(function(i,r){var n,s=((n={key:r})["data-rh"]=!0,n);return Object.keys(i).forEach(function(l){var o=gt[l]||l;o==="innerHTML"||o==="cssText"?s.dangerouslySetInnerHTML={__html:i.innerHTML||i.cssText}:s[o]=i[l]}),oe.createElement(t,s)})},xe=function(t,a,i){switch(t){case F.TITLE:return{toComponent:function(){return n=a.titleAttributes,(s={key:r=a.title})["data-rh"]=!0,l=or(n,s),[oe.createElement(F.TITLE,l,r)];var r,n,s,l},toString:function(){return function(r,n,s,l){var o=lr(s),u=Sr(n);return o?"<"+r+' data-rh="true" '+o+">"+Pt(u,l)+"</"+r+">":"<"+r+' data-rh="true">'+Pt(u,l)+"</"+r+">"}(t,a.title,a.titleAttributes,i)}};case"bodyAttributes":case"htmlAttributes":return{toComponent:function(){return or(a)},toString:function(){return lr(a)}};default:return{toComponent:function(){return ut(t,a)},toString:function(){return function(r,n,s){return n.reduce(function(l,o){var u=Object.keys(o).filter(function(f){return!(f==="innerHTML"||f==="cssText")}).reduce(function(f,N){var v=o[N]===void 0?N:N+'="'+Pt(o[N],s)+'"';return f?f+" "+v:v},""),h=o.innerHTML||o.cssText||"",m=Ga.indexOf(r)===-1;return l+"<"+r+' data-rh="true" '+u+(m?"/>":">"+h+"</"+r+">")},"")}(t,a,i)}}}},Dt=function(t){var a=t.baseTag,i=t.bodyAttributes,r=t.encode,n=t.htmlAttributes,s=t.noscriptTags,l=t.styleTags,o=t.title,u=o===void 0?"":o,h=t.titleAttributes,m=t.linkTags,f=t.metaTags,N=t.scriptTags,v={toComponent:function(){},toString:function(){return""}};if(t.prioritizeSeoTags){var d=function(w){var A=w.linkTags,S=w.scriptTags,b=w.encode,R=Et(w.metaTags,$a),V=Et(A,Ua),j=Et(S,Ba);return{priorityMethods:{toComponent:function(){return[].concat(ut(F.META,R.priority),ut(F.LINK,V.priority),ut(F.SCRIPT,j.priority))},toString:function(){return xe(F.META,R.priority,b)+" "+xe(F.LINK,V.priority,b)+" "+xe(F.SCRIPT,j.priority,b)}},metaTags:R.default,linkTags:V.default,scriptTags:j.default}}(t);v=d.priorityMethods,m=d.linkTags,f=d.metaTags,N=d.scriptTags}return{priority:v,base:xe(F.BASE,a,r),bodyAttributes:xe("bodyAttributes",i,r),htmlAttributes:xe("htmlAttributes",n,r),link:xe(F.LINK,m,r),meta:xe(F.META,f,r),noscript:xe(F.NOSCRIPT,s,r),script:xe(F.SCRIPT,N,r),style:xe(F.STYLE,l,r),title:xe(F.TITLE,{title:u,titleAttributes:h},r)}},it=[],Ft=function(t,a){var i=this;a===void 0&&(a=typeof document<"u"),this.instances=[],this.value={setHelmet:function(r){i.context.helmet=r},helmetInstances:{get:function(){return i.canUseDOM?it:i.instances},add:function(r){(i.canUseDOM?it:i.instances).push(r)},remove:function(r){var n=(i.canUseDOM?it:i.instances).indexOf(r);(i.canUseDOM?it:i.instances).splice(n,1)}}},this.context=t,this.canUseDOM=a,a||(t.helmet=Dt({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))},Or=oe.createContext({}),Wa=M.shape({setHelmet:M.func,helmetInstances:M.shape({get:M.func,add:M.func,remove:M.func})}),Xa=typeof document<"u",Qe=function(t){function a(i){var r;return(r=t.call(this,i)||this).helmetData=new Ft(r.props.context,a.canUseDOM),r}return Bt(a,t),a.prototype.render=function(){return oe.createElement(Or.Provider,{value:this.helmetData.value},this.props.children)},a}(P.Component);Qe.canUseDOM=Xa,Qe.propTypes={context:M.shape({helmet:M.shape()}),children:M.node.isRequired},Qe.defaultProps={context:{}},Qe.displayName="HelmetProvider";var $e=function(t,a){var i,r=document.head||document.querySelector(F.HEAD),n=r.querySelectorAll(t+"[data-rh]"),s=[].slice.call(n),l=[];return a&&a.length&&a.forEach(function(o){var u=document.createElement(t);for(var h in o)Object.prototype.hasOwnProperty.call(o,h)&&(h==="innerHTML"?u.innerHTML=o.innerHTML:h==="cssText"?u.styleSheet?u.styleSheet.cssText=o.cssText:u.appendChild(document.createTextNode(o.cssText)):u.setAttribute(h,o[h]===void 0?"":o[h]));u.setAttribute("data-rh","true"),s.some(function(m,f){return i=f,u.isEqualNode(m)})?s.splice(i,1):l.push(u)}),s.forEach(function(o){return o.parentNode.removeChild(o)}),l.forEach(function(o){return r.appendChild(o)}),{oldTags:s,newTags:l}},At=function(t,a){var i=document.getElementsByTagName(t)[0];if(i){for(var r=i.getAttribute("data-rh"),n=r?r.split(","):[],s=[].concat(n),l=Object.keys(a),o=0;o<l.length;o+=1){var u=l[o],h=a[u]||"";i.getAttribute(u)!==h&&i.setAttribute(u,h),n.indexOf(u)===-1&&n.push(u);var m=s.indexOf(u);m!==-1&&s.splice(m,1)}for(var f=s.length-1;f>=0;f-=1)i.removeAttribute(s[f]);n.length===s.length?i.removeAttribute("data-rh"):i.getAttribute("data-rh")!==l.join(",")&&i.setAttribute("data-rh",l.join(","))}},cr=function(t,a){var i=t.baseTag,r=t.htmlAttributes,n=t.linkTags,s=t.metaTags,l=t.noscriptTags,o=t.onChangeClientState,u=t.scriptTags,h=t.styleTags,m=t.title,f=t.titleAttributes;At(F.BODY,t.bodyAttributes),At(F.HTML,r),function(w,A){w!==void 0&&document.title!==w&&(document.title=Sr(w)),At(F.TITLE,A)}(m,f);var N={baseTag:$e(F.BASE,i),linkTags:$e(F.LINK,n),metaTags:$e(F.META,s),noscriptTags:$e(F.NOSCRIPT,l),scriptTags:$e(F.SCRIPT,u),styleTags:$e(F.STYLE,h)},v={},d={};Object.keys(N).forEach(function(w){var A=N[w],S=A.newTags,b=A.oldTags;S.length&&(v[w]=S),b.length&&(d[w]=N[w].oldTags)}),a&&a(),o(t,v,d)},Ge=null,vt=function(t){function a(){for(var r,n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return(r=t.call.apply(t,[this].concat(s))||this).rendered=!1,r}Bt(a,t);var i=a.prototype;return i.shouldComponentUpdate=function(r){return!Ha(r,this.props)},i.componentDidUpdate=function(){this.emitChange()},i.componentWillUnmount=function(){this.props.context.helmetInstances.remove(this),this.emitChange()},i.emitChange=function(){var r,n,s=this.props.context,l=s.setHelmet,o=null,u=(r=s.helmetInstances.get().map(function(h){var m=pe({},h.props);return delete m.context,m}),{baseTag:Va(["href"],r),bodyAttributes:Ct("bodyAttributes",r),defer:Ke(r,"defer"),encode:Ke(r,"encodeSpecialCharacters"),htmlAttributes:Ct("htmlAttributes",r),linkTags:Ye(F.LINK,["rel","href"],r),metaTags:Ye(F.META,["name","charset","http-equiv","property","itemprop"],r),noscriptTags:Ye(F.NOSCRIPT,["innerHTML"],r),onChangeClientState:Ka(r),scriptTags:Ye(F.SCRIPT,["src","innerHTML"],r),styleTags:Ye(F.STYLE,["cssText"],r),title:Qa(r),titleAttributes:Ct("titleAttributes",r),prioritizeSeoTags:Ya(r,"prioritizeSeoTags")});Qe.canUseDOM?(n=u,Ge&&cancelAnimationFrame(Ge),n.defer?Ge=requestAnimationFrame(function(){cr(n,function(){Ge=null})}):(cr(n),Ge=null)):Dt&&(o=Dt(u)),l(o)},i.init=function(){this.rendered||(this.rendered=!0,this.props.context.helmetInstances.add(this),this.emitChange())},i.render=function(){return this.init(),null},a}(P.Component);vt.propTypes={context:Wa.isRequired},vt.displayName="HelmetDispatcher";var Ja=["children"],Za=["children"],Ee=function(t){function a(){return t.apply(this,arguments)||this}Bt(a,t);var i=a.prototype;return i.shouldComponentUpdate=function(r){return!Da(sr(this.props,"helmetData"),sr(r,"helmetData"))},i.mapNestedChildrenToProps=function(r,n){if(!n)return null;switch(r.type){case F.SCRIPT:case F.NOSCRIPT:return{innerHTML:n};case F.STYLE:return{cssText:n};default:throw new Error("<"+r.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")}},i.flattenArrayTypeChildren=function(r){var n,s=r.child,l=r.arrayTypeChildren;return pe({},l,((n={})[s.type]=[].concat(l[s.type]||[],[pe({},r.newChildProps,this.mapNestedChildrenToProps(s,r.nestedChildren))]),n))},i.mapObjectTypeChildren=function(r){var n,s,l=r.child,o=r.newProps,u=r.newChildProps,h=r.nestedChildren;switch(l.type){case F.TITLE:return pe({},o,((n={})[l.type]=h,n.titleAttributes=pe({},u),n));case F.BODY:return pe({},o,{bodyAttributes:pe({},u)});case F.HTML:return pe({},o,{htmlAttributes:pe({},u)});default:return pe({},o,((s={})[l.type]=pe({},u),s))}},i.mapArrayTypeChildrenToProps=function(r,n){var s=pe({},n);return Object.keys(r).forEach(function(l){var o;s=pe({},s,((o={})[l]=r[l],o))}),s},i.warnOnInvalidChildren=function(r,n){return ar(ir.some(function(s){return r.type===s}),typeof r.type=="function"?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":"Only elements types "+ir.join(", ")+" are allowed. Helmet does not support rendering <"+r.type+"> elements. Refer to our API for more information."),ar(!n||typeof n=="string"||Array.isArray(n)&&!n.some(function(s){return typeof s!="string"}),"Helmet expects a string as a child of <"+r.type+">. Did you forget to wrap your children in braces? ( <"+r.type+">{``}</"+r.type+"> ) Refer to our API for more information."),!0},i.mapChildrenToProps=function(r,n){var s=this,l={};return oe.Children.forEach(r,function(o){if(o&&o.props){var u=o.props,h=u.children,m=nr(u,Ja),f=Object.keys(m).reduce(function(v,d){return v[qa[d]||d]=m[d],v},{}),N=o.type;switch(typeof N=="symbol"?N=N.toString():s.warnOnInvalidChildren(o,h),N){case F.FRAGMENT:n=s.mapChildrenToProps(h,n);break;case F.LINK:case F.META:case F.NOSCRIPT:case F.SCRIPT:case F.STYLE:l=s.flattenArrayTypeChildren({child:o,arrayTypeChildren:l,newChildProps:f,nestedChildren:h});break;default:n=s.mapObjectTypeChildren({child:o,newProps:n,newChildProps:f,nestedChildren:h})}}}),this.mapArrayTypeChildrenToProps(l,n)},i.render=function(){var r=this.props,n=r.children,s=nr(r,Za),l=pe({},s),o=s.helmetData;return n&&(l=this.mapChildrenToProps(n,l)),!o||o instanceof Ft||(o=new Ft(o.context,o.instances)),o?oe.createElement(vt,pe({},l,{context:o.value,helmetData:void 0})):oe.createElement(Or.Consumer,null,function(u){return oe.createElement(vt,pe({},l,{context:u}))})},a}(P.Component);Ee.propTypes={base:M.object,bodyAttributes:M.object,children:M.oneOfType([M.arrayOf(M.node),M.node]),defaultTitle:M.string,defer:M.bool,encodeSpecialCharacters:M.bool,htmlAttributes:M.object,link:M.arrayOf(M.object),meta:M.arrayOf(M.object),noscript:M.arrayOf(M.object),onChangeClientState:M.func,script:M.arrayOf(M.object),style:M.arrayOf(M.object),title:M.string,titleAttributes:M.object,titleTemplate:M.string,prioritizeSeoTags:M.bool,helmetData:M.object},Ee.defaultProps={defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1},Ee.displayName="Helmet";const en="https://metaanalizhaber.com",Te=qr.create({baseURL:en,timeout:15e3,headers:{"Content-Type":"application/json"}}),Le=!1;Te.interceptors.request.use(t=>t,t=>Promise.reject(t));Te.interceptors.response.use(t=>t.data,t=>(console.error("API Error:",t),Promise.reject(t)));const fe={getNews:async(t={})=>{try{const a=Le?"/backend/mock_api.php":"/backend/api.php",i={sort:"pub_date",order:"DESC",...t};return await Te.get(a,{params:{action:"get_news",...i}})}catch(a){return console.error("Error fetching news:",a),{success:!1,data:[],total:0,error:a.message}}},getNewsBySlug:async t=>{try{const a=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(a,{params:{action:"get_news_by_slug",slug:t}})}catch(a){throw console.error("Error fetching news by slug:",a),a}},getNewsDetail:async t=>{try{const a=Le?"/backend/mock_api.php":"/backend/api.php";return/^\d{8}[A-Z]{2}\d{6}$/.test(t)?await Te.get(a,{params:{action:"get_news_detail",haber_kodu:t}}):await Te.get(a,{params:{action:"get_news_by_slug",slug:t}})}catch(a){throw console.error("API Error in getNewsDetail:",a),a}},getCategories:async()=>{try{const t=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(t,{params:{action:"get_categories"}})}catch(t){throw console.error("Error fetching categories:",t),t}},getCities:async()=>{try{const t=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(t,{params:{action:"get_cities"}})}catch(t){throw console.error("Error fetching cities:",t),t}},getWorldNews:async(t={})=>{try{const a=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(a,{params:{action:"get_world_news",...t}})}catch(a){throw console.error("Error fetching world news:",a),a}},getVideoNews:async(t={})=>{try{const a=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(a,{params:{action:"get_video_news",...t}})}catch(a){throw console.error("Error fetching video news:",a),a}},searchNews:async(t,a={})=>{try{const i=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(i,{params:{action:"search_news",q:t,...a}})}catch(i){throw console.error("Error searching news:",i),i}},getCurrencyRates:async()=>{try{const t=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(t,{params:{action:"get_currency_rates"}})}catch(t){throw console.error("Error fetching currency rates:",t),t}},getStockIndices:async()=>{try{const t=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(t,{params:{action:"get_stock_indices"}})}catch(t){throw console.error("Error fetching stock indices:",t),t}},getCommodities:async()=>{try{const t=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(t,{params:{action:"get_commodities"}})}catch(t){throw console.error("Error fetching commodities:",t),t}},getStocks:async()=>{try{const t=Le?"/backend/mock_api.php":"/backend/api.php";return await Te.get(t,{params:{action:"get_stocks"}})}catch(t){throw console.error("Error fetching stocks:",t),t}}},Se=t=>{if(!t)return"";const a=t.replace(/<[^>]*>/g,""),i=document.createElement("textarea");return i.innerHTML=a,i.value.replace(/\s+/g," ").trim()},We=t=>{if(!t)return"";let a=t;const i=document.createElement("textarea");return i.innerHTML=a,a=i.value,a=a.replace(/<\/p>/gi,`

`),a=a.replace(/<p[^>]*>/gi,""),a=a.replace(/<br\s*\/?>/gi,`
`),a=a.replace(/<[^>]*>/g,""),a=a.replace(/[ \t]+/g," "),a=a.replace(/\n\s*\n\s*\n+/g,`

`),a=a.replace(/\n\s+/g,`
`),a=a.replace(/\s+\n/g,`
`),a.trim()},Ce=t=>{if(!t)return"";const a=document.createElement("textarea");return a.innerHTML=t,a.value},tn=()=>{const[t,a]=P.useState(0),i=l=>new Date(l).toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"}),{data:r,isLoading:n}=ye("breaking-news",()=>fe.getNews({breaking:"true",limit:50}),{refetchInterval:6e4,select:l=>{if(l!=null&&l.success&&(l!=null&&l.data)){const o=Array.isArray(l.data)?l.data:[],u=new Date,h=new Date(u.getTime()-6*60*60*1e3);return o.filter(f=>{const N=new Date(f.pub_date),v=f.son_dakika==="Evet",d=N>=h;return v&&d}).sort((f,N)=>new Date(N.pub_date)-new Date(f.pub_date)).slice(0,10)}return[]}});if(P.useEffect(()=>{if(r&&r.length>1){const l=setInterval(()=>{a(o=>(o+1)%r.length)},4e3);return()=>clearInterval(l)}},[r==null?void 0:r.length]),n||!r||r.length===0)return null;const s=r[t];return e("div",{className:"breaking-news-bar",children:e("div",{className:"container",children:c("div",{className:"breaking-news-content",children:[e("div",{className:"breaking-label",children:e("span",{className:"breaking-text",children:"Son Dakika"})}),e("div",{className:"breaking-news-slider",children:s?c(z,{to:`/haber/${s.slug||s.haber_kodu}`,className:"breaking-news-item",children:[e("h4",{className:"breaking-title",children:Se(s.title).replace(/\n/g," ").trim()}),e("span",{className:"breaking-time",children:i(s.pub_date)})]}):e("div",{className:"breaking-news-item",children:e("h4",{className:"breaking-title",children:"Haber yükleniyor..."})})}),r.length>1&&e("div",{className:"breaking-indicators",children:r.map((l,o)=>e("button",{className:`indicator ${o===t?"active":""}`,onClick:()=>a(o),"aria-label":`Son dakika haberi ${o+1}`},`indicator-${l.haber_kodu||o}`))})]})})})};let qe=null,Fe=null;window.tradingViewTickerLoaded||(window.tradingViewTickerLoaded=!1);function rn(){const t=P.useRef(),a=P.useRef(),i=P.useRef(!1);return P.useEffect(()=>{if(qe&&Fe!==t.current)try{Fe&&(Fe.innerHTML=""),qe=null,Fe=null}catch(r){console.warn("Ticker widget cleanup error:",r)}if(!(qe&&Fe===t.current))return t.current&&(t.current.innerHTML="",a.current&&a.current.parentNode&&a.current.parentNode.removeChild(a.current),document.querySelectorAll(".ticker-tape-container iframe").forEach(n=>{n.src&&n.src.includes("tradingview.com")&&n.src.includes("ticker-tape")&&n.remove()}),setTimeout(()=>{if(t.current&&!qe&&!window.tradingViewTickerLoaded){if(document.querySelector('script[src*="embed-widget-ticker-tape.js"]')){window.tradingViewTickerLoaded=!0;return}const s=document.createElement("script");s.src="https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js",s.type="text/javascript",s.async=!0,s.innerHTML=`
              {
                "symbols": [
                  {
                    "proName": "FOREXCOM:NSXUSD",
                    "title": "US 100 Cash CFD"
                  },
                  {
                    "proName": "FX_IDC:EURUSD",
                    "title": "EUR to USD"
                  },
                  {
                    "proName": "BITSTAMP:BTCUSD",
                    "title": "Bitcoin"
                  },
                  {
                    "proName": "BITSTAMP:ETHUSD",
                    "title": "Ethereum"
                  }
                ],
                "colorTheme": "light",
                "locale": "tr",
                "largeChartUrl": "",
                "isTransparent": false,
                "showSymbolLogo": true,
                "displayMode": "regular"
              }`,s.onload=()=>{window.tradingViewTickerLoaded=!0},a.current=s,t.current.appendChild(s),qe=s,Fe=t.current,i.current=!0}},100)),()=>{Fe===t.current&&(qe=null,Fe=null),i.current=!1,t.current&&(t.current.innerHTML=""),a.current&&a.current.parentNode&&(a.current.parentNode.removeChild(a.current),a.current=null)}},[]),e("div",{id:"ticker-tape-widget-unique",className:"tradingview-widget-container ticker-tape-container",ref:t,children:e("div",{className:"tradingview-widget-container__widget"})})}const an=P.memo(rn),ur=t=>{if(!t)return"";const a={ç:"c",Ç:"c",ğ:"g",Ğ:"g",ı:"i",I:"i",İ:"i",ö:"o",Ö:"o",ş:"s",Ş:"s",ü:"u",Ü:"u"};return t.split("").map(i=>a[i]||i).join("").toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()},nn=t=>({asayis:"ASAYIŞ",genel:"GENEL","haberde-insan":"HABERDE İNSAN",cevre:"ÇEVRE",politika:"POLİTİKA",spor:"SPOR","kultur-sanat":"KÜLTÜR SANAT",egitim:"EĞİTİM",ekonomi:"EKONOMİ",saglik:"SAĞLIK","bilim-ve-teknoloji":"BİLİM VE TEKNOLOJİ",magazin:"MAGAZİN",dunya:"DIŞ HABER"})[t]||t.toUpperCase(),bt=t=>t?{SAGLIK:"SAĞLIK",ASAYIS:"ASAYIŞ",CEVRE:"ÇEVRE",POLITIKA:"POLİTİKA",EGITIM:"EĞİTİM",EKONOMI:"EKONOMİ","KULTUR SANAT":"KÜLTÜR SANAT","BILIM VE TEKNOLOJI":"BİLİM VE TEKNOLOJİ",MAGAZIN:"MAGAZİN"}[t]||t:"",sn=()=>{const[t,a]=P.useState(!1),[i,r]=P.useState(!1),{data:n}=ye("categories",()=>fe.getCategories(),{staleTime:6e5}),s=n!=null&&n.success&&n.data?(()=>{const l=[...n.data];console.log("Available categories:",l.map(f=>f.name||f));const o=["GENEL","VİDEO HABERLER","DÜNYA","ASAYİŞ"],u=[],h=[];l.forEach(f=>{const N=(f.name||f).toUpperCase(),v=o.indexOf(N);v!==-1?u[v]=f:h.push(f)});const m=u.filter(Boolean);return console.log("Sorted categories:",[...m,...h].map(f=>f.name||f)),[...m,...h]})():[];return c(ie,{children:[c("header",{className:"header-main",children:[c("div",{className:"container",children:[c("div",{className:"main-header",children:[c("button",{className:"hamburger-menu",onClick:()=>r(!i),"aria-label":"Menüyü aç/kapat",children:[e("span",{}),e("span",{}),e("span",{})]}),c("div",{className:"logo-slogan-container",children:[e("div",{className:"logo",children:e(z,{to:"/",children:e("img",{src:"/logo7.webp",alt:"Meta Analiz Haber",className:"logo-image",style:{height:"50px"}})})}),e("div",{className:"header-slogan",children:e("span",{children:"Özgür ve Bağımsız Haber..."})})]})]}),e("nav",{className:"main-nav",children:c("div",{className:"nav-container",children:[c("ul",{className:`nav-menu ${t?"active":""}`,children:[e("li",{className:"nav-item",children:e(Be,{to:"/",className:"nav-link",onClick:()=>a(!1),children:"ANA SAYFA"})}),e("li",{className:"nav-item",children:e(Be,{to:"/kategori/genel",className:"nav-link",onClick:()=>a(!1),children:"GENEL"})}),e("li",{className:"nav-item",children:e(Be,{to:"/video-haberler",className:"nav-link",onClick:()=>a(!1),children:"VİDEO HABERLER"})}),e("li",{className:"nav-item",children:e(Be,{to:"/dunya-haberleri",className:"nav-link",onClick:()=>a(!1),children:"DÜNYA"})}),s.filter(l=>!["GENEL","Genel"].includes(l.name)).map(l=>e("li",{className:"nav-item",children:e(Be,{to:`/kategori/${ur(l.name)}`,className:"nav-link",onClick:()=>a(!1),children:bt(l.name)})},l.name)),e("li",{className:"nav-item",children:e(Be,{to:"/yerel-haberler",className:"nav-link",onClick:()=>a(!1),children:"YEREL HABERLER"})})]}),c("div",{className:"mobile-menu-toggle",onClick:()=>a(!t),"aria-label":"Menüyü aç/kapat",children:[e("span",{}),e("span",{}),e("span",{})]})]})}),e(tn,{})]}),e(an,{})]}),e("div",{className:`sidebar-overlay ${i?"active":""}`,onClick:()=>r(!1),children:c("div",{className:`sidebar ${i?"active":""}`,onClick:l=>l.stopPropagation(),children:[c("div",{className:"sidebar-header",children:[e("h3",{children:"KATEGORİLER"}),e("button",{className:"sidebar-close",onClick:()=>r(!1),children:e("i",{className:"fas fa-times"})})]}),e("div",{className:"sidebar-content",children:c("ul",{className:"sidebar-menu",children:[e("li",{children:c(z,{to:"/",onClick:()=>r(!1),children:[e("i",{className:"fas fa-home"})," ANA SAYFA"]})}),e("li",{children:e(z,{to:"/kategori/genel",onClick:()=>r(!1),children:"GENEL"})}),e("li",{children:e(z,{to:"/video-haberler",onClick:()=>r(!1),children:"VİDEO HABERLER"})}),e("li",{children:e(z,{to:"/dunya-haberleri",onClick:()=>r(!1),children:"DÜNYA"})}),s.length>0?s.filter(l=>!["GENEL","Genel"].includes(l.name)).map(l=>e("li",{children:e(z,{to:`/kategori/${ur(l.name)}`,onClick:()=>r(!1),children:bt(l.name)})},l.name)):e("li",{children:e("span",{children:"Kategoriler yükleniyor..."})}),e("li",{children:e(z,{to:"/yerel-haberler",onClick:()=>r(!1),children:"YEREL HABERLER"})})]})})]})})]})},ln=()=>{const t=new Date().getFullYear();return e("footer",{className:"modern-footer",children:c("div",{className:"container",children:[c("div",{className:"footer-main",children:[e("div",{className:"footer-logos",children:c("div",{className:"logo-group-vertical",children:[e("img",{src:"/logo7.webp",alt:"Meta Analiz Haber",className:"footer-logo-main"}),e("p",{className:"footer-tagline",children:"Özgür ve Bağımsız Haber..."}),e("div",{className:"partner-section",children:e("img",{src:"/iha_logo.webp",alt:"İHA - İhlas Haber Ajansı",className:"footer-logo-partner"})})]})}),c("div",{className:"footer-social",children:[e("h4",{className:"footer-section-title",children:"Sosyal Medya"}),c("div",{className:"social-links-modern",children:[c("a",{href:"https://x.com/MetaAnaliz",className:"social-link-modern","aria-label":"X (Twitter)",target:"_blank",rel:"noopener noreferrer",children:[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),e("span",{children:"Twitter"})]}),c("a",{href:"https://www.facebook.com/metaanalizhaber",className:"social-link-modern","aria-label":"Facebook",target:"_blank",rel:"noopener noreferrer",children:[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),e("span",{children:"Facebook"})]}),c("a",{href:"https://www.instagram.com/metaanalizhaber",className:"social-link-modern","aria-label":"Instagram",target:"_blank",rel:"noopener noreferrer",children:[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})}),e("span",{children:"Instagram"})]}),c("a",{href:"https://www.youtube.com/@metaanaliz",className:"social-link-modern","aria-label":"YouTube",target:"_blank",rel:"noopener noreferrer",children:[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})}),e("span",{children:"YouTube"})]})]})]}),c("div",{className:"footer-contact",children:[e("h4",{className:"footer-section-title",children:"İletişim"}),c("div",{className:"contact-info-modern",children:[c("div",{className:"contact-item",children:[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"})}),e("span",{children:"<EMAIL>"})]}),c("div",{className:"contact-item",children:[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"})}),e("span",{children:"+90 (542) 380 00 50"})]}),c("div",{className:"contact-item",children:[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"})}),e("span",{children:"Aydın, Türkiye"})]})]})]})]}),c("div",{className:"footer-bottom-modern",children:[e("div",{className:"footer-copyright",children:c("p",{children:["© ",t," Meta Analiz Haber. Tüm hakları saklıdır."]})}),c("div",{className:"footer-links",children:[e("a",{href:"/kunye-bilgileri",children:"Künye Bilgileri"}),e("a",{href:"/gizlilik-politikasi",children:"Gizlilik Politikası"}),e("a",{href:"/kullanici-politikalari",children:"Kullanıcı Politikaları"})]})]})]})})},on=()=>{const{pathname:t}=jr();return P.useEffect(()=>{window.scrollTo(0,0)},[t]),null},Cr=P.createContext(),cn=({children:t})=>{const[a,i]=P.useState([]),[r,n]=P.useState(""),{data:s,isLoading:l}=ye("categories",fe.getCategories,{staleTime:6e5,onSuccess:f=>{f&&f.success&&f.data&&i(f.data)}}),o=P.useCallback(f=>new Date(f).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),[]),u=P.useCallback(f=>{if(!f)return"";const N=new Date;let v;if(f.includes(" ")?v=new Date(f.replace(" ","T")):v=new Date(f),isNaN(v.getTime()))return f;const d=Math.floor((N-v)/1e3);return d<0||d<60?"Az önce":d<3600?`${Math.floor(d/60)} dakika önce`:d<86400?`${Math.floor(d/3600)} saat önce`:d<604800?`${Math.floor(d/86400)} gün önce`:v.toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},[]),h=P.useCallback((f,N)=>f?f.length<=N?f:f.substr(0,N)+"...":"",[]),m=P.useMemo(()=>({categories:a,categoriesLoading:l,currentCategory:r,setCurrentCategory:n,formatDate:o,getRelativeTime:u,truncateText:h}),[a,l,r,o,u,h]);return e(Cr.Provider,{value:m,children:t})},Pe=()=>{const t=P.useContext(Cr);if(t===void 0)throw new Error("useNews must be used within a NewsProvider");return t};const un=()=>{const{getRelativeTime:t}=Pe(),{data:a,isLoading:i,error:r}=ye("worldNews",()=>fe.getWorldNews({limit:20}),{refetchInterval:5*60*1e3,staleTime:2*60*1e3,cacheTime:10*60*1e3}),n=a!=null&&a.success?a.data:[],s=n.filter(o=>o.main_image&&o.main_image!=="/assets/images/placeholder.svg"&&o.main_image.trim()!==""),l=n.filter(o=>!o.main_image||o.main_image==="/assets/images/placeholder.svg"||o.main_image.trim()==="");return i?e("div",{className:"world-news-section",children:c("div",{className:"container",children:[e("div",{className:"world-section-header",children:e("h2",{className:"world-section-title",children:"Dünya Haberleri"})}),c("div",{className:"world-loading",children:[e("div",{className:"loading-spinner"}),e("p",{children:"Dünya haberleri yükleniyor..."})]})]})}):r||!n.length?e("div",{className:"world-news-section",children:c("div",{className:"container",children:[e("div",{className:"world-section-header",children:e("h2",{className:"world-section-title",children:"Dünya Haberleri"})}),e("div",{className:"world-error",children:e("p",{children:"Dünya haberleri şu anda yüklenemiyor."})})]})}):e("div",{className:"world-news-section",children:c("div",{className:"container",children:[e("div",{className:"world-section-header",children:e("h2",{className:"world-section-title",children:"Dünya Haberleri"})}),c("div",{className:"world-news-layout",children:[s.length>0&&e("div",{className:"world-news-grid",children:s.slice(0,8).map((o,u)=>e("div",{className:"world-grid-item",children:c(z,{to:`/haber/${o.slug||o.haber_kodu}`,className:"world-news-card",children:[c("div",{className:"world-card-image",children:[e("img",{src:o.main_image||o.featured_image||"/assets/images/placeholder.svg",alt:o.title,onError:h=>{h.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"world-card-category",children:o.kategori})]}),c("div",{className:"world-card-content",children:[e("h3",{className:"world-card-title",children:o.title}),e("div",{className:"world-card-meta",children:e("span",{className:"world-card-date",children:o.formatted_date||t(o.pub_date)})})]})]})},o.id))}),l.length>0&&c("div",{className:"world-news-list",children:[e("h4",{className:"world-list-title",children:"Diğer Dünya Haberleri"}),l.slice(0,7).map((o,u)=>e("div",{className:"world-list-item",children:c(z,{to:`/haber/${o.slug||o.haber_kodu}`,className:"world-list-content",children:[e("span",{className:"world-list-category",children:o.kategori}),e("h4",{className:"world-list-title-text",children:o.title}),e("div",{className:"world-list-meta",children:e("span",{className:"world-list-date",children:t(o.pub_date)})})]})},o.id))]})]})]})})};var Er={exports:{}};(()=>{var t={296:(n,s,l)=>{var o=/^\s+|\s+$/g,u=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,m=/^0o[0-7]+$/i,f=parseInt,N=typeof l.g=="object"&&l.g&&l.g.Object===Object&&l.g,v=typeof self=="object"&&self&&self.Object===Object&&self,d=N||v||Function("return this")(),w=Object.prototype.toString,A=Math.max,S=Math.min,b=function(){return d.Date.now()};function R(j){var D=typeof j;return!!j&&(D=="object"||D=="function")}function V(j){if(typeof j=="number")return j;if(function(H){return typeof H=="symbol"||function(Y){return!!Y&&typeof Y=="object"}(H)&&w.call(H)=="[object Symbol]"}(j))return NaN;if(R(j)){var D=typeof j.valueOf=="function"?j.valueOf():j;j=R(D)?D+"":D}if(typeof j!="string")return j===0?j:+j;j=j.replace(o,"");var le=h.test(j);return le||m.test(j)?f(j.slice(2),le?2:8):u.test(j)?NaN:+j}n.exports=function(j,D,le){var H,Y,W,te,G,X,y=0,C=!1,E=!1,U=!0;if(typeof j!="function")throw new TypeError("Expected a function");function B(ee){var ce=H,ve=Y;return H=Y=void 0,y=ee,te=j.apply(ve,ce)}function Z(ee){var ce=ee-X;return X===void 0||ce>=D||ce<0||E&&ee-y>=W}function q(){var ee=b();if(Z(ee))return ae(ee);G=setTimeout(q,function(ce){var ve=D-(ce-X);return E?S(ve,W-(ce-y)):ve}(ee))}function ae(ee){return G=void 0,U&&H?B(ee):(H=Y=void 0,te)}function ne(){var ee=b(),ce=Z(ee);if(H=arguments,Y=this,X=ee,ce){if(G===void 0)return function(ve){return y=ve,G=setTimeout(q,D),C?B(ve):te}(X);if(E)return G=setTimeout(q,D),B(X)}return G===void 0&&(G=setTimeout(q,D)),te}return D=V(D)||0,R(le)&&(C=!!le.leading,W=(E="maxWait"in le)?A(V(le.maxWait)||0,D):W,U="trailing"in le?!!le.trailing:U),ne.cancel=function(){G!==void 0&&clearTimeout(G),y=0,H=X=Y=G=void 0},ne.flush=function(){return G===void 0?te:ae(b())},ne}},96:(n,s,l)=>{var o="Expected a function",u=NaN,h="[object Symbol]",m=/^\s+|\s+$/g,f=/^[-+]0x[0-9a-f]+$/i,N=/^0b[01]+$/i,v=/^0o[0-7]+$/i,d=parseInt,w=typeof l.g=="object"&&l.g&&l.g.Object===Object&&l.g,A=typeof self=="object"&&self&&self.Object===Object&&self,S=w||A||Function("return this")(),b=Object.prototype.toString,R=Math.max,V=Math.min,j=function(){return S.Date.now()};function D(H){var Y=typeof H;return!!H&&(Y=="object"||Y=="function")}function le(H){if(typeof H=="number")return H;if(function(te){return typeof te=="symbol"||function(G){return!!G&&typeof G=="object"}(te)&&b.call(te)==h}(H))return u;if(D(H)){var Y=typeof H.valueOf=="function"?H.valueOf():H;H=D(Y)?Y+"":Y}if(typeof H!="string")return H===0?H:+H;H=H.replace(m,"");var W=N.test(H);return W||v.test(H)?d(H.slice(2),W?2:8):f.test(H)?u:+H}n.exports=function(H,Y,W){var te=!0,G=!0;if(typeof H!="function")throw new TypeError(o);return D(W)&&(te="leading"in W?!!W.leading:te,G="trailing"in W?!!W.trailing:G),function(X,y,C){var E,U,B,Z,q,ae,ne=0,ee=!1,ce=!1,ve=!0;if(typeof X!="function")throw new TypeError(o);function Ae(be){var Re=E,Oe=U;return E=U=void 0,ne=be,Z=X.apply(Oe,Re)}function tt(be){var Re=be-ae;return ae===void 0||Re>=y||Re<0||ce&&be-ne>=B}function Ie(){var be=j();if(tt(be))return Ve(be);q=setTimeout(Ie,function(Re){var Oe=y-(Re-ae);return ce?V(Oe,B-(Re-ne)):Oe}(be))}function Ve(be){return q=void 0,ve&&E?Ae(be):(E=U=void 0,Z)}function De(){var be=j(),Re=tt(be);if(E=arguments,U=this,ae=be,Re){if(q===void 0)return function(Oe){return ne=Oe,q=setTimeout(Ie,y),ee?Ae(Oe):Z}(ae);if(ce)return q=setTimeout(Ie,y),Ae(ae)}return q===void 0&&(q=setTimeout(Ie,y)),Z}return y=le(y)||0,D(C)&&(ee=!!C.leading,B=(ce="maxWait"in C)?R(le(C.maxWait)||0,y):B,ve="trailing"in C?!!C.trailing:ve),De.cancel=function(){q!==void 0&&clearTimeout(q),ne=0,E=ae=U=q=void 0},De.flush=function(){return q===void 0?Z:Ve(j())},De}(H,Y,{leading:te,maxWait:Y,trailing:G})}},703:(n,s,l)=>{var o=l(414);function u(){}function h(){}h.resetWarningCache=u,n.exports=function(){function m(v,d,w,A,S,b){if(b!==o){var R=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw R.name="Invariant Violation",R}}function f(){return m}m.isRequired=m;var N={array:m,bigint:m,bool:m,func:m,number:m,object:m,string:m,symbol:m,any:m,arrayOf:f,element:m,elementType:m,instanceOf:f,node:m,objectOf:f,oneOf:f,oneOfType:f,shape:f,exact:f,checkPropTypes:h,resetWarningCache:u};return N.PropTypes=N,N}},697:(n,s,l)=>{n.exports=l(703)()},414:n=>{n.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},a={};function i(n){var s=a[n];if(s!==void 0)return s.exports;var l=a[n]={exports:{}};return t[n](l,l.exports,i),l.exports}i.n=n=>{var s=n&&n.__esModule?()=>n.default:()=>n;return i.d(s,{a:s}),s},i.d=(n,s)=>{for(var l in s)i.o(s,l)&&!i.o(n,l)&&Object.defineProperty(n,l,{enumerable:!0,get:s[l]})},i.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),i.o=(n,s)=>Object.prototype.hasOwnProperty.call(n,s),i.r=n=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var r={};(()=>{i.r(r),i.d(r,{LazyLoadComponent:()=>Re,LazyLoadImage:()=>Ir,trackWindowScroll:()=>Z});const n=P;var s=i.n(n),l=i(697);function o(){return typeof window<"u"&&"IntersectionObserver"in window&&"isIntersecting"in window.IntersectionObserverEntry.prototype}function u(_){return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},u(_)}function h(_,p){var T=Object.keys(_);if(Object.getOwnPropertySymbols){var O=Object.getOwnPropertySymbols(_);p&&(O=O.filter(function($){return Object.getOwnPropertyDescriptor(_,$).enumerable})),T.push.apply(T,O)}return T}function m(_,p,T){return(p=N(p))in _?Object.defineProperty(_,p,{value:T,enumerable:!0,configurable:!0,writable:!0}):_[p]=T,_}function f(_,p){for(var T=0;T<p.length;T++){var O=p[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(_,N(O.key),O)}}function N(_){var p=function(T,O){if(u(T)!=="object"||T===null)return T;var $=T[Symbol.toPrimitive];if($!==void 0){var Q=$.call(T,"string");if(u(Q)!=="object")return Q;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(T)}(_);return u(p)==="symbol"?p:String(p)}function v(_,p){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(T,O){return T.__proto__=O,T},v(_,p)}function d(_){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(p){return p.__proto__||Object.getPrototypeOf(p)},d(_)}var w=function(_){_.forEach(function(p){p.isIntersecting&&p.target.onVisible()})},A={},S=function(_){(function(g,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(k&&k.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),k&&v(g,k)})(re,_);var p,T,O,$,Q=(O=re,$=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,k=d(O);if($){var I=d(this).constructor;g=Reflect.construct(k,arguments,I)}else g=k.apply(this,arguments);return function(L,x){if(x&&(u(x)==="object"||typeof x=="function"))return x;if(x!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(K){if(K===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return K}(L)}(this,g)});function re(g){var k;if(function(L,x){if(!(L instanceof x))throw new TypeError("Cannot call a class as a function")}(this,re),(k=Q.call(this,g)).supportsObserver=!g.scrollPosition&&g.useIntersectionObserver&&o(),k.supportsObserver){var I=g.threshold;k.observer=function(L){return A[L]=A[L]||new IntersectionObserver(w,{rootMargin:L+"px"}),A[L]}(I)}return k}return p=re,T=[{key:"componentDidMount",value:function(){this.placeholder&&this.observer&&(this.placeholder.onVisible=this.props.onVisible,this.observer.observe(this.placeholder)),this.supportsObserver||this.updateVisibility()}},{key:"componentWillUnmount",value:function(){this.observer&&this.placeholder&&this.observer.unobserve(this.placeholder)}},{key:"componentDidUpdate",value:function(){this.supportsObserver||this.updateVisibility()}},{key:"getPlaceholderBoundingBox",value:function(){var g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props.scrollPosition,k=this.placeholder.getBoundingClientRect(),I=this.placeholder.style,L=parseInt(I.getPropertyValue("margin-left"),10)||0,x=parseInt(I.getPropertyValue("margin-top"),10)||0;return{bottom:g.y+k.bottom+x,left:g.x+k.left+L,right:g.x+k.right+L,top:g.y+k.top+x}}},{key:"isPlaceholderInViewport",value:function(){if(typeof window>"u"||!this.placeholder)return!1;var g=this.props,k=g.scrollPosition,I=g.threshold,L=this.getPlaceholderBoundingBox(k),x=k.y+window.innerHeight,K=k.x,ue=k.x+window.innerWidth,de=k.y;return de-I<=L.bottom&&x+I>=L.top&&K-I<=L.right&&ue+I>=L.left}},{key:"updateVisibility",value:function(){this.isPlaceholderInViewport()&&this.props.onVisible()}},{key:"render",value:function(){var g=this,k=this.props,I=k.className,L=k.height,x=k.placeholder,K=k.style,ue=k.width;if(x&&typeof x.type!="function")return s().cloneElement(x,{ref:function(se){return g.placeholder=se}});var de=function(se){for(var Ne=1;Ne<arguments.length;Ne++){var ge=arguments[Ne]!=null?arguments[Ne]:{};Ne%2?h(Object(ge),!0).forEach(function(we){m(se,we,ge[we])}):Object.getOwnPropertyDescriptors?Object.defineProperties(se,Object.getOwnPropertyDescriptors(ge)):h(Object(ge)).forEach(function(we){Object.defineProperty(se,we,Object.getOwnPropertyDescriptor(ge,we))})}return se}({display:"inline-block"},K);return ue!==void 0&&(de.width=ue),L!==void 0&&(de.height=L),s().createElement("span",{className:I,ref:function(se){return g.placeholder=se},style:de},x)}}],T&&f(p.prototype,T),Object.defineProperty(p,"prototype",{writable:!1}),re}(s().Component);S.propTypes={onVisible:l.PropTypes.func.isRequired,className:l.PropTypes.string,height:l.PropTypes.oneOfType([l.PropTypes.number,l.PropTypes.string]),placeholder:l.PropTypes.element,threshold:l.PropTypes.number,useIntersectionObserver:l.PropTypes.bool,scrollPosition:l.PropTypes.shape({x:l.PropTypes.number.isRequired,y:l.PropTypes.number.isRequired}),width:l.PropTypes.oneOfType([l.PropTypes.number,l.PropTypes.string])},S.defaultProps={className:"",placeholder:null,threshold:100,useIntersectionObserver:!0};const b=S;var R=i(296),V=i.n(R),j=i(96),D=i.n(j),le=function(_){var p=getComputedStyle(_,null);return p.getPropertyValue("overflow")+p.getPropertyValue("overflow-y")+p.getPropertyValue("overflow-x")};const H=function(_){if(!(_ instanceof HTMLElement))return window;for(var p=_;p&&p instanceof HTMLElement;){if(/(scroll|auto)/.test(le(p)))return p;p=p.parentNode}return window};function Y(_){return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},Y(_)}var W=["delayMethod","delayTime"];function te(){return te=Object.assign?Object.assign.bind():function(_){for(var p=1;p<arguments.length;p++){var T=arguments[p];for(var O in T)Object.prototype.hasOwnProperty.call(T,O)&&(_[O]=T[O])}return _},te.apply(this,arguments)}function G(_,p){for(var T=0;T<p.length;T++){var O=p[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(_,($=function(Q,re){if(Y(Q)!=="object"||Q===null)return Q;var g=Q[Symbol.toPrimitive];if(g!==void 0){var k=g.call(Q,"string");if(Y(k)!=="object")return k;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(Q)}(O.key),Y($)==="symbol"?$:String($)),O)}var $}function X(_,p){return X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(T,O){return T.__proto__=O,T},X(_,p)}function y(_,p){if(p&&(Y(p)==="object"||typeof p=="function"))return p;if(p!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return C(_)}function C(_){if(_===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _}function E(_){return E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(p){return p.__proto__||Object.getPrototypeOf(p)},E(_)}var U=function(){return typeof window>"u"?0:window.scrollX||window.pageXOffset},B=function(){return typeof window>"u"?0:window.scrollY||window.pageYOffset};const Z=function(_){var p=function(T){(function(I,L){if(typeof L!="function"&&L!==null)throw new TypeError("Super expression must either be null or a function");I.prototype=Object.create(L&&L.prototype,{constructor:{value:I,writable:!0,configurable:!0}}),Object.defineProperty(I,"prototype",{writable:!1}),L&&X(I,L)})(k,T);var O,$,Q,re,g=(Q=k,re=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var I,L=E(Q);if(re){var x=E(this).constructor;I=Reflect.construct(L,arguments,x)}else I=L.apply(this,arguments);return y(this,I)});function k(I){var L;if(function(K,ue){if(!(K instanceof ue))throw new TypeError("Cannot call a class as a function")}(this,k),(L=g.call(this,I)).useIntersectionObserver=I.useIntersectionObserver&&o(),L.useIntersectionObserver)return y(L);var x=L.onChangeScroll.bind(C(L));return I.delayMethod==="debounce"?L.delayedScroll=V()(x,I.delayTime):I.delayMethod==="throttle"&&(L.delayedScroll=D()(x,I.delayTime)),L.state={scrollPosition:{x:U(),y:B()}},L.baseComponentRef=s().createRef(),L}return O=k,($=[{key:"componentDidMount",value:function(){this.addListeners()}},{key:"componentWillUnmount",value:function(){this.removeListeners()}},{key:"componentDidUpdate",value:function(){typeof window>"u"||this.useIntersectionObserver||H(this.baseComponentRef.current)!==this.scrollElement&&(this.removeListeners(),this.addListeners())}},{key:"addListeners",value:function(){typeof window>"u"||this.useIntersectionObserver||(this.scrollElement=H(this.baseComponentRef.current),this.scrollElement.addEventListener("scroll",this.delayedScroll,{passive:!0}),window.addEventListener("resize",this.delayedScroll,{passive:!0}),this.scrollElement!==window&&window.addEventListener("scroll",this.delayedScroll,{passive:!0}))}},{key:"removeListeners",value:function(){typeof window>"u"||this.useIntersectionObserver||(this.scrollElement.removeEventListener("scroll",this.delayedScroll),window.removeEventListener("resize",this.delayedScroll),this.scrollElement!==window&&window.removeEventListener("scroll",this.delayedScroll))}},{key:"onChangeScroll",value:function(){this.useIntersectionObserver||this.setState({scrollPosition:{x:U(),y:B()}})}},{key:"render",value:function(){var I=this.props,L=(I.delayMethod,I.delayTime,function(K,ue){if(K==null)return{};var de,se,Ne=function(we,Ue){if(we==null)return{};var ze,nt,Kt={},Vt=Object.keys(we);for(nt=0;nt<Vt.length;nt++)ze=Vt[nt],Ue.indexOf(ze)>=0||(Kt[ze]=we[ze]);return Kt}(K,ue);if(Object.getOwnPropertySymbols){var ge=Object.getOwnPropertySymbols(K);for(se=0;se<ge.length;se++)de=ge[se],ue.indexOf(de)>=0||Object.prototype.propertyIsEnumerable.call(K,de)&&(Ne[de]=K[de])}return Ne}(I,W)),x=this.useIntersectionObserver?null:this.state.scrollPosition;return s().createElement(_,te({forwardRef:this.baseComponentRef,scrollPosition:x},L))}}])&&G(O.prototype,$),Object.defineProperty(O,"prototype",{writable:!1}),k}(s().Component);return p.propTypes={delayMethod:l.PropTypes.oneOf(["debounce","throttle"]),delayTime:l.PropTypes.number,useIntersectionObserver:l.PropTypes.bool},p.defaultProps={delayMethod:"throttle",delayTime:300,useIntersectionObserver:!0},p};function q(_){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},q(_)}function ae(_,p){for(var T=0;T<p.length;T++){var O=p[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(_,($=function(Q,re){if(q(Q)!=="object"||Q===null)return Q;var g=Q[Symbol.toPrimitive];if(g!==void 0){var k=g.call(Q,"string");if(q(k)!=="object")return k;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(Q)}(O.key),q($)==="symbol"?$:String($)),O)}var $}function ne(_,p){return ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(T,O){return T.__proto__=O,T},ne(_,p)}function ee(_){return ee=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(p){return p.__proto__||Object.getPrototypeOf(p)},ee(_)}var ce=function(_){(function(g,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(k&&k.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),k&&ne(g,k)})(re,_);var p,T,O,$,Q=(O=re,$=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,k=ee(O);if($){var I=ee(this).constructor;g=Reflect.construct(k,arguments,I)}else g=k.apply(this,arguments);return function(L,x){if(x&&(q(x)==="object"||typeof x=="function"))return x;if(x!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(K){if(K===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return K}(L)}(this,g)});function re(g){return function(k,I){if(!(k instanceof I))throw new TypeError("Cannot call a class as a function")}(this,re),Q.call(this,g)}return p=re,(T=[{key:"render",value:function(){return s().createElement(b,this.props)}}])&&ae(p.prototype,T),Object.defineProperty(p,"prototype",{writable:!1}),re}(s().Component);const ve=Z(ce);function Ae(_){return Ae=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},Ae(_)}function tt(_,p){for(var T=0;T<p.length;T++){var O=p[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(_,($=function(Q,re){if(Ae(Q)!=="object"||Q===null)return Q;var g=Q[Symbol.toPrimitive];if(g!==void 0){var k=g.call(Q,"string");if(Ae(k)!=="object")return k;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(Q)}(O.key),Ae($)==="symbol"?$:String($)),O)}var $}function Ie(_,p){return Ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(T,O){return T.__proto__=O,T},Ie(_,p)}function Ve(_){if(_===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _}function De(_){return De=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(p){return p.__proto__||Object.getPrototypeOf(p)},De(_)}var be=function(_){(function(g,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(k&&k.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),k&&Ie(g,k)})(re,_);var p,T,O,$,Q=(O=re,$=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,k=De(O);if($){var I=De(this).constructor;g=Reflect.construct(k,arguments,I)}else g=k.apply(this,arguments);return function(L,x){if(x&&(Ae(x)==="object"||typeof x=="function"))return x;if(x!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ve(L)}(this,g)});function re(g){var k;(function(ue,de){if(!(ue instanceof de))throw new TypeError("Cannot call a class as a function")})(this,re),k=Q.call(this,g);var I=g.afterLoad,L=g.beforeLoad,x=g.scrollPosition,K=g.visibleByDefault;return k.state={visible:K},K&&(L(),I()),k.onVisible=k.onVisible.bind(Ve(k)),k.isScrollTracked=!!(x&&Number.isFinite(x.x)&&x.x>=0&&Number.isFinite(x.y)&&x.y>=0),k}return p=re,(T=[{key:"componentDidUpdate",value:function(g,k){k.visible!==this.state.visible&&this.props.afterLoad()}},{key:"onVisible",value:function(){this.props.beforeLoad(),this.setState({visible:!0})}},{key:"render",value:function(){if(this.state.visible)return this.props.children;var g=this.props,k=g.className,I=g.delayMethod,L=g.delayTime,x=g.height,K=g.placeholder,ue=g.scrollPosition,de=g.style,se=g.threshold,Ne=g.useIntersectionObserver,ge=g.width;return this.isScrollTracked||Ne&&o()?s().createElement(b,{className:k,height:x,onVisible:this.onVisible,placeholder:K,scrollPosition:ue,style:de,threshold:se,useIntersectionObserver:Ne,width:ge}):s().createElement(ve,{className:k,delayMethod:I,delayTime:L,height:x,onVisible:this.onVisible,placeholder:K,style:de,threshold:se,width:ge})}}])&&tt(p.prototype,T),Object.defineProperty(p,"prototype",{writable:!1}),re}(s().Component);be.propTypes={afterLoad:l.PropTypes.func,beforeLoad:l.PropTypes.func,useIntersectionObserver:l.PropTypes.bool,visibleByDefault:l.PropTypes.bool},be.defaultProps={afterLoad:function(){return{}},beforeLoad:function(){return{}},useIntersectionObserver:!0,visibleByDefault:!1};const Re=be;function Oe(_){return Oe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},Oe(_)}var xr=["afterLoad","beforeLoad","delayMethod","delayTime","effect","placeholder","placeholderSrc","scrollPosition","threshold","useIntersectionObserver","visibleByDefault","wrapperClassName","wrapperProps"];function $t(_,p){var T=Object.keys(_);if(Object.getOwnPropertySymbols){var O=Object.getOwnPropertySymbols(_);p&&(O=O.filter(function($){return Object.getOwnPropertyDescriptor(_,$).enumerable})),T.push.apply(T,O)}return T}function qt(_){for(var p=1;p<arguments.length;p++){var T=arguments[p]!=null?arguments[p]:{};p%2?$t(Object(T),!0).forEach(function(O){Lr(_,O,T[O])}):Object.getOwnPropertyDescriptors?Object.defineProperties(_,Object.getOwnPropertyDescriptors(T)):$t(Object(T)).forEach(function(O){Object.defineProperty(_,O,Object.getOwnPropertyDescriptor(T,O))})}return _}function Lr(_,p,T){return(p=Qt(p))in _?Object.defineProperty(_,p,{value:T,enumerable:!0,configurable:!0,writable:!0}):_[p]=T,_}function rt(){return rt=Object.assign?Object.assign.bind():function(_){for(var p=1;p<arguments.length;p++){var T=arguments[p];for(var O in T)Object.prototype.hasOwnProperty.call(T,O)&&(_[O]=T[O])}return _},rt.apply(this,arguments)}function Mr(_,p){for(var T=0;T<p.length;T++){var O=p[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(_,Qt(O.key),O)}}function Qt(_){var p=function(T,O){if(Oe(T)!=="object"||T===null)return T;var $=T[Symbol.toPrimitive];if($!==void 0){var Q=$.call(T,"string");if(Oe(Q)!=="object")return Q;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(T)}(_);return Oe(p)==="symbol"?p:String(p)}function St(_,p){return St=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(T,O){return T.__proto__=O,T},St(_,p)}function at(_){return at=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(p){return p.__proto__||Object.getPrototypeOf(p)},at(_)}var Ot=function(_){(function(g,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");g.prototype=Object.create(k&&k.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),k&&St(g,k)})(re,_);var p,T,O,$,Q=(O=re,$=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}(),function(){var g,k=at(O);if($){var I=at(this).constructor;g=Reflect.construct(k,arguments,I)}else g=k.apply(this,arguments);return function(L,x){if(x&&(Oe(x)==="object"||typeof x=="function"))return x;if(x!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(K){if(K===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return K}(L)}(this,g)});function re(g){var k;return function(I,L){if(!(I instanceof L))throw new TypeError("Cannot call a class as a function")}(this,re),(k=Q.call(this,g)).state={loaded:!1},k}return p=re,(T=[{key:"onImageLoad",value:function(){var g=this;return this.state.loaded?null:function(k){g.props.onLoad(k),g.props.afterLoad(),g.setState({loaded:!0})}}},{key:"getImg",value:function(){var g=this.props,k=(g.afterLoad,g.beforeLoad,g.delayMethod,g.delayTime,g.effect,g.placeholder,g.placeholderSrc,g.scrollPosition,g.threshold,g.useIntersectionObserver,g.visibleByDefault,g.wrapperClassName,g.wrapperProps,function(I,L){if(I==null)return{};var x,K,ue=function(se,Ne){if(se==null)return{};var ge,we,Ue={},ze=Object.keys(se);for(we=0;we<ze.length;we++)ge=ze[we],Ne.indexOf(ge)>=0||(Ue[ge]=se[ge]);return Ue}(I,L);if(Object.getOwnPropertySymbols){var de=Object.getOwnPropertySymbols(I);for(K=0;K<de.length;K++)x=de[K],L.indexOf(x)>=0||Object.prototype.propertyIsEnumerable.call(I,x)&&(ue[x]=I[x])}return ue}(g,xr));return s().createElement("img",rt({},k,{onLoad:this.onImageLoad()}))}},{key:"getLazyLoadImage",value:function(){var g=this.props,k=g.beforeLoad,I=g.className,L=g.delayMethod,x=g.delayTime,K=g.height,ue=g.placeholder,de=g.scrollPosition,se=g.style,Ne=g.threshold,ge=g.useIntersectionObserver,we=g.visibleByDefault,Ue=g.width;return s().createElement(Re,{beforeLoad:k,className:I,delayMethod:L,delayTime:x,height:K,placeholder:ue,scrollPosition:de,style:se,threshold:Ne,useIntersectionObserver:ge,visibleByDefault:we,width:Ue},this.getImg())}},{key:"getWrappedLazyLoadImage",value:function(g){var k=this.props,I=k.effect,L=k.height,x=k.placeholderSrc,K=k.width,ue=k.wrapperClassName,de=k.wrapperProps,se=this.state.loaded,Ne=se?" lazy-load-image-loaded":"",ge=se||!x?{}:{backgroundImage:"url(".concat(x,")"),backgroundSize:"100% 100%"};return s().createElement("span",rt({className:ue+" lazy-load-image-background "+I+Ne,style:qt(qt({},ge),{},{color:"transparent",display:"inline-block",height:L,width:K})},de),g)}},{key:"render",value:function(){var g=this.props,k=g.effect,I=g.placeholderSrc,L=g.visibleByDefault,x=g.wrapperClassName,K=g.wrapperProps,ue=this.getLazyLoadImage();return(k||I)&&!L||x||K?this.getWrappedLazyLoadImage(ue):ue}}])&&Mr(p.prototype,T),Object.defineProperty(p,"prototype",{writable:!1}),re}(s().Component);Ot.propTypes={onLoad:l.PropTypes.func,afterLoad:l.PropTypes.func,beforeLoad:l.PropTypes.func,delayMethod:l.PropTypes.string,delayTime:l.PropTypes.number,effect:l.PropTypes.string,placeholderSrc:l.PropTypes.string,threshold:l.PropTypes.number,useIntersectionObserver:l.PropTypes.bool,visibleByDefault:l.PropTypes.bool,wrapperClassName:l.PropTypes.string,wrapperProps:l.PropTypes.object},Ot.defaultProps={onLoad:function(){},afterLoad:function(){return{}},beforeLoad:function(){return{}},delayMethod:"throttle",delayTime:300,effect:"",placeholderSrc:null,threshold:100,useIntersectionObserver:!0,visibleByDefault:!1,wrapperClassName:""};const Ir=Ot})(),Er.exports=r})();var Me=Er.exports;const dn=({news:t=[]})=>{const{getRelativeTime:a}=Pe(),[i,r]=P.useState(0);P.useEffect(()=>{if(t.length<=1)return;const o=setInterval(()=>{r(u=>(u+1)%t.length)},5e3);return()=>clearInterval(o)},[t.length]);const n=o=>o.slug&&o.slug.trim()!==""?`/haber/${o.slug}`:`/haber/${o.haber_kodu}`,s=o=>{o.target.src="/assets/images/placeholder.svg"};if(!t||t.length===0)return e("div",{className:"hero-slider-container",children:e("div",{className:"hero-slider-placeholder",children:e("p",{children:"Haberler yükleniyor..."})})});const l=t[i];return c("div",{className:"hero-slider-container",children:[c("div",{className:"hero-slider",children:[e("div",{className:"hero-slide",children:e(z,{to:n(l),className:"hero-slide-link",children:c("div",{className:"hero-image",children:[e(Me.LazyLoadImage,{src:l.main_image||l.featured_image||"/assets/images/placeholder.svg",alt:Ce(Se(l.title)),effect:"blur",onError:s,placeholderSrc:"/assets/images/placeholder.svg"}),e("div",{className:"hero-overlay",children:c("div",{className:"hero-content",children:[l.son_dakika==="Evet"&&e("div",{className:"hero-badges",children:e("span",{className:"hero-breaking",children:"SON DAKİKA"})}),e("h1",{className:"hero-title",children:Ce(Se(l.title))}),l.summary&&e("p",{className:"hero-summary",children:Ce(Se(l.summary))}),c("div",{className:"hero-meta",children:[l.kategori&&e("span",{className:"hero-category",children:l.kategori}),e("span",{className:"hero-date",children:l.formatted_date||a(l.pub_date||l.tarih)})]})]})})]})})}),t.length>1&&e("div",{className:"hero-navigation",children:t.map((o,u)=>e("button",{className:`hero-dot ${u===i?"active":""}`,onClick:()=>r(u),"aria-label":`Slide ${u+1}'e git`},u))}),t.length>1&&c(ie,{children:[e("button",{className:"hero-arrow hero-arrow-prev",onClick:()=>r(o=>(o-1+t.length)%t.length),"aria-label":"Önceki haber",children:e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e("button",{className:"hero-arrow hero-arrow-next",onClick:()=>r(o=>(o+1)%t.length),"aria-label":"Sonraki haber",children:e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]}),t.length>5&&c("div",{className:"hero-sidebar",children:[e("h3",{className:"sidebar-title",children:"Neler Oldu?"}),e("div",{className:"sidebar-news-list",children:t.slice(1,6).map((o,u)=>c(z,{to:n(o),className:"sidebar-news-item",children:[e("div",{className:"sidebar-news-image",children:e(Me.LazyLoadImage,{src:o.main_image||o.featured_image||"/assets/images/placeholder.svg",alt:Ce(Se(o.title)),effect:"blur",onError:s,placeholderSrc:"/assets/images/placeholder.svg"})}),c("div",{className:"sidebar-news-content",children:[e("h4",{className:"sidebar-news-title",children:Ce(Se(o.title))}),e("span",{className:"sidebar-news-date",children:a(o.pub_date||o.tarih)})]})]},o.haber_kodu))})]})]})};function hn(){const t=P.useRef(),a=P.useRef(),i=P.useRef(!1);return P.useEffect(()=>{if(!i.current&&t.current){t.current.innerHTML="",a.current&&a.current.parentNode&&a.current.parentNode.removeChild(a.current),document.querySelectorAll(".tradingview-widget-container iframe").forEach(s=>{s.src&&s.src.includes("tradingview.com")&&s.remove()});const n=document.createElement("script");n.src="https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js",n.type="text/javascript",n.async=!0,n.innerHTML=`
        {
          "colorTheme": "light",
          "dateRange": "12M",
          "locale": "tr",
          "largeChartUrl": "",
          "isTransparent": false,
          "showFloatingTooltip": false,
          "plotLineColorGrowing": "rgba(41, 98, 255, 1)",
          "plotLineColorFalling": "rgba(41, 98, 255, 1)",
          "gridLineColor": "rgba(240, 243, 250, 0)",
          "scaleFontColor": "#0F0F0F",
          "belowLineFillColorGrowing": "rgba(41, 98, 255, 0.12)",
          "belowLineFillColorFalling": "rgba(41, 98, 255, 0.12)",
          "belowLineFillColorGrowingBottom": "rgba(41, 98, 255, 0)",
          "belowLineFillColorFallingBottom": "rgba(41, 98, 255, 0)",
          "symbolActiveColor": "rgba(41, 98, 255, 0.12)",
          "tabs": [
            {
              "title": "Kripto",
              "symbols": [
                {
                  "s": "BINANCE:BTCUSDT",
                  "d": "Bitcoin USD",
                  "base-currency-logoid": "crypto/XTVCBTC",
                  "currency-logoid": "crypto/XTVCUSDT"
                },
                {
                  "s": "BINANCE:ETHUSDT",
                  "d": "Ethereum",
                  "base-currency-logoid": "crypto/XTVCETH",
                  "currency-logoid": "crypto/XTVCUSDT"
                },
                {
                  "s": "NASDAQ:TSLA",
                  "d": "Tesla",
                  "logoid": "tesla",
                  "currency-logoid": "country/US"
                },
                {
                  "s": "NASDAQ:META",
                  "d": "Meta",
                  "logoid": "meta-platforms",
                  "currency-logoid": "country/US"
                },
                {
                  "s": "NASDAQ:GOOGL",
                  "d": "Google",
                  "logoid": "alphabet",
                  "currency-logoid": "country/US"
                },
                {
                  "s": "NASDAQ:NFLX",
                  "d": "Netflix",
                  "logoid": "netflix",
                  "currency-logoid": "country/US"
                }
              ],
              "originalTitle": "Kripto"
            },
            {
              "title": "Vadeli İşlemler",
              "symbols": [
                {
                  "s": "BMFBOVESPA:ISP1!",
                  "d": "S&P 500"
                },
                {
                  "s": "BMFBOVESPA:EUR1!",
                  "d": "Euro"
                },
                {
                  "s": "CMCMARKETS:GOLD",
                  "d": "Altın"
                },
                {
                  "s": "PYTH:WTI3!",
                  "d": "WTI Ham Petrol"
                },
                {
                  "s": "BMFBOVESPA:CCM1!",
                  "d": "Mısır"
                }
              ],
              "originalTitle": "Vadeli İşlemler"
            },
            {
              "title": "Tahviller",
              "symbols": [
                {
                  "s": "EUREX:FGBL1!",
                  "d": "Euro Bund"
                },
                {
                  "s": "EUREX:FBTP1!",
                  "d": "Euro BTP"
                },
                {
                  "s": "EUREX:FGBM1!",
                  "d": "Euro BOBL"
                }
              ],
              "originalTitle": "Tahviller"
            },
            {
              "title": "Döviz",
              "symbols": [
                {
                  "s": "FX:EURUSD",
                  "d": "EUR/USD"
                },
                {
                  "s": "FX:GBPUSD",
                  "d": "GBP/USD"
                },
                {
                  "s": "FX:USDJPY",
                  "d": "USD/JPY"
                },
                {
                  "s": "FX:USDCHF",
                  "d": "USD/CHF"
                },
                {
                  "s": "FX:AUDUSD",
                  "d": "AUD/USD"
                },
                {
                  "s": "FX:USDCAD",
                  "d": "USD/CAD"
                }
              ],
              "originalTitle": "Döviz"
            }
          ],
          "support_host": "https://www.tradingview.com",
          "backgroundColor": "#131722",
          "width": "100%",
          "height": "580",
          "showSymbolLogo": true,
          "showChart": true
        }`,a.current=n,t.current.appendChild(n),i.current=!0}},[]),P.useEffect(()=>()=>{i.current=!1,t.current&&(t.current.innerHTML=""),a.current&&a.current.parentNode&&(a.current.parentNode.removeChild(a.current),a.current=null),document.querySelectorAll(".tradingview-widget-container iframe").forEach(n=>{n.src&&n.src.includes("tradingview.com")&&n.remove()})},[]),e("div",{className:"tradingview-widget-container",ref:t,children:e("div",{className:"tradingview-widget-container__widget"})})}const Pr=P.memo(hn);function mn(){const t=P.useRef();return P.useEffect(()=>{const a=document.createElement("script");a.src="https://s3.tradingview.com/external-embedding/embed-widget-forex-cross-rates.js",a.type="text/javascript",a.async=!0,a.innerHTML=`
        {
          "width": "100%",
          "height": 400,
          "currencies": [
            "EUR",
            "USD",
            "TRY",
            "GBP",
            "JPY",
            "AUD",
            "CAD",
            "CHF"
          ],
          "isTransparent": false,
          "colorTheme": "light",
          "locale": "tr",
          "backgroundColor": "#ffffff"
        }`,t.current.appendChild(a)},[]),e("div",{className:"tradingview-widget-container forex-rates-container",ref:t,children:e("div",{className:"tradingview-widget-container__widget"})})}const Ar=P.memo(mn),he=({size:t="medium",text:a="Yükleniyor..."})=>{const i={small:"spinner-border-sm",medium:"",large:"spinner-border-lg"}[t];return c("div",{className:"loading-spinner-container",children:[e("div",{className:`spinner-border text-primary ${i}`,role:"status",children:e("span",{className:"visually-hidden",children:a})}),a&&e("div",{className:"loading-text mt-2",children:a})]})};const fn=()=>{const{getRelativeTime:t}=Pe(),a=d=>d.slug&&d.slug.trim()!==""?`/haber/${d.slug}`:`/haber/${d.haber_kodu}`,{data:i,isLoading:r}=ye("homepage-news",()=>fe.getNews({limit:100,sort:"pub_date",order:"DESC"}),{staleTime:6e4,cacheTime:3e5,refetchInterval:12e4}),{data:n}=ye("homepage-economy",()=>fe.getNews({kategori:"EKONOMİ",limit:9,sort:"pub_date",order:"DESC"}),{staleTime:3e4,cacheTime:6e4,refetchInterval:6e4,refetchOnWindowFocus:!0}),{data:s}=ye("homepage-sports",()=>fe.getNews({kategori:"SPOR",limit:8,sort:"pub_date",order:"DESC"}),{staleTime:3e4,cacheTime:6e4,refetchInterval:6e4,refetchOnWindowFocus:!0}),l=i!=null&&i.success&&Array.isArray(i.data)?i.data:[];l.length>0&&console.log("İlk haber zaman bilgisi:",{title:l[0].title,pub_date:l[0].pub_date,relativeTime:t(l[0].pub_date)});const o=l.filter(d=>d.main_image&&d.main_image!==null&&d.main_image!=="/assets/images/placeholder.svg"&&d.main_image.trim()!==""&&!d.main_image.includes("placeholder")).sort((d,w)=>new Date(w.pub_date)-new Date(d.pub_date)),u=o.slice(0,15),h=o.filter(d=>!u.some(w=>w.haber_kodu===d.haber_kodu)).slice(0,4);let m=o.filter(d=>!u.some(w=>w.haber_kodu===d.haber_kodu)&&!h.some(w=>w.haber_kodu===d.haber_kodu)).slice(0,8);if(m.length<8){const d=o.filter(w=>!m.some(A=>A.haber_kodu===w.haber_kodu)).slice(0,8-m.length);m=[...m,...d]}console.log("Debug Info:",{totalNews:l.length,newsWithImages:o.length,heroNews:u.length,sidebarNews:h.length,featuredNews:m.length}),m.length>0&&console.log("Featured news zaman testi:",{news_title:m[0].title,pub_date:m[0].pub_date,relative_time:t(m[0].pub_date)}),console.log("Zaman bilgisi debug - İlk 3 haber:",m.slice(0,3).map(d=>{var w;return{title:((w=d.title)==null?void 0:w.substring(0,50))+"...",pub_date:d.pub_date,relative_time:t(d.pub_date)}}));const f=o.filter(d=>!m.some(w=>w.haber_kodu===d.haber_kodu)).slice(0,8),N=n!=null&&n.success&&Array.isArray(n.data)?n.data:[],v=s!=null&&s.success&&Array.isArray(s.data)?s.data:[];return r?e("div",{className:"loading-container",children:e(he,{})}):c(ie,{children:[c(Ee,{children:[e("title",{children:"Meta Analiz Haber - Özgür ve Bağımsız Haber..."}),e("meta",{name:"description",content:"Türkiye'den ve dünyadan en güncel haberler."})]}),c("div",{className:"new-homepage",children:[e("section",{className:"hero-section",children:e("div",{className:"container",children:e(dn,{news:u})})}),e("section",{className:"featured-section",children:c("div",{className:"container",children:[e("div",{className:"section-header",children:e("h2",{className:"section-title",children:"Öne Çıkan Haberler"})}),e("div",{className:"news-grid force-show-time",children:m.map(d=>e("article",{className:"news-card",children:c(z,{to:a(d),children:[c("div",{className:"card-image",children:[e(Me.LazyLoadImage,{src:d.main_image||d.featured_image||"/assets/images/placeholder.svg",alt:d.title,effect:"blur",onError:w=>{w.target.src="/assets/images/placeholder.svg"}}),d.kategori&&e("span",{className:"card-category",children:d.kategori})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:Ce(Se(d.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:t(d.pub_date)})})]})]})},d.haber_kodu))})]})}),e("section",{className:"latest-section",children:c("div",{className:"container",children:[c("div",{className:"section-header",children:[e("h2",{className:"section-title",children:"Son Haberler"}),e(z,{to:"/son-haberler",className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"news-grid force-show-time",children:f.map(d=>e("article",{className:"news-card",children:c(z,{to:a(d),children:[c("div",{className:"card-image",children:[e(Me.LazyLoadImage,{src:d.main_image||d.featured_image||"/assets/images/placeholder.svg",alt:d.title,effect:"blur",onError:w=>{w.target.src="/assets/images/placeholder.svg"}}),d.kategori&&e("span",{className:"card-category",children:d.kategori})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:Ce(Se(d.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:t(d.pub_date)})})]})]})},d.haber_kodu))})]})}),e(un,{}),N.length>0&&e("section",{className:"category-section economy-section",children:c("div",{className:"container",children:[c("div",{className:"section-header",children:[e("h2",{className:"section-title",children:"Ekonomi Haberleri"}),e(z,{to:"/kategori/ekonomi",className:"section-link",children:"Tümünü Gör"})]}),c("div",{className:"economy-content-wrapper",children:[e("div",{className:"economy-news-grid",children:e("div",{className:"news-grid force-show-time",children:N.slice(0,9).map(d=>e("article",{className:"news-card",children:c(z,{to:a(d),children:[c("div",{className:"card-image",children:[e(Me.LazyLoadImage,{src:d.main_image||d.featured_image||"/assets/images/placeholder.svg",alt:d.title,effect:"blur",onError:w=>{w.target.src="/assets/images/placeholder.svg"}}),e("span",{className:"card-category",children:"EKONOMİ"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:Ce(Se(d.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:t(d.pub_date)})})]})]})},d.haber_kodu))})}),e("div",{className:"economy-sidebar",children:c("div",{className:"economy-widgets-stack",children:[e(Pr,{}),e(Ar,{})]})})]})]})}),v.length>0&&e("section",{className:"category-section",children:c("div",{className:"container",children:[c("div",{className:"section-header",children:[e("h2",{className:"section-title",children:"Spor Haberleri"}),e(z,{to:"/kategori/spor",className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"news-grid force-show-time",children:v.slice(0,8).map(d=>e("article",{className:"news-card",children:c(z,{to:a(d),children:[c("div",{className:"card-image",children:[e(Me.LazyLoadImage,{src:d.main_image||d.featured_image||"/assets/images/placeholder.svg",alt:d.title,effect:"blur",onError:w=>{w.target.src="/assets/images/placeholder.svg"}}),e("span",{className:"card-category",children:"SPOR"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:Ce(Se(d.title))}),e("div",{className:"card-meta",children:e("span",{className:"card-date",children:t(d.pub_date)})})]})]})},d.haber_kodu))})]})})]})]})};const et=({message:t,onRetry:a})=>e("div",{className:"error-message-container",children:c("div",{className:"alert alert-danger",role:"alert",children:[c("h4",{className:"alert-heading",children:[e("i",{className:"fas fa-exclamation-triangle me-2"}),"Hata!"]}),e("p",{children:t||"Bir hata oluştu. Lütfen daha sonra tekrar deneyin."}),a&&e("div",{className:"mt-3",children:c("button",{className:"btn btn-outline-danger",onClick:a,children:[e("i",{className:"fas fa-sync-alt me-2"}),"Tekrar Dene"]})})]})});function pn(){const t=P.useRef(),a=P.useRef(),i=P.useRef(!1);return P.useEffect(()=>{if(!i.current&&t.current){t.current.innerHTML="",a.current&&a.current.parentNode&&a.current.parentNode.removeChild(a.current);const r=document.createElement("script");r.src="https://s3.tradingview.com/external-embedding/embed-widget-hotlists.js",r.type="text/javascript",r.async=!0,r.innerHTML=`
          {
            "exchange": "US",
            "colorTheme": "light",
            "dateRange": "1D",
            "showChart": true,
            "locale": "tr",
            "largeChartUrl": "",
            "isTransparent": false,
            "showSymbolLogo": false,
            "showFloatingTooltip": false,
            "plotLineColorGrowing": "rgba(41, 98, 255, 1)",
            "plotLineColorFalling": "rgba(41, 98, 255, 1)",
            "gridLineColor": "rgba(240, 243, 250, 0)",
            "scaleFontColor": "#0F0F0F",
            "belowLineFillColorGrowing": "rgba(41, 98, 255, 0.12)",
            "belowLineFillColorFalling": "rgba(41, 98, 255, 0.12)",
            "belowLineFillColorGrowingBottom": "rgba(41, 98, 255, 0)",
            "belowLineFillColorFallingBottom": "rgba(41, 98, 255, 0)",
            "symbolActiveColor": "rgba(41, 98, 255, 0.12)",
            "width": "400",
            "height": "550"
          }`,a.current=r,t.current.appendChild(r),i.current=!0}},[]),P.useEffect(()=>()=>{i.current=!1,t.current&&(t.current.innerHTML=""),a.current&&a.current.parentNode&&(a.current.parentNode.removeChild(a.current),a.current=null)},[]),c("div",{className:"tradingview-widget-container hotlists-container",ref:t,children:[e("div",{className:"tradingview-widget-container__widget"}),e("div",{className:"tradingview-widget-copyright",children:e("a",{href:"https://tr.tradingview.com/",rel:"noopener nofollow",target:"_blank",children:e("span",{className:"blue-text",children:"Track all markets on TradingView"})})})]})}const yn=P.memo(pn),gn=()=>{var S;const{categoryName:t}=jt(),[a,i]=P.useState(1),[r,n]=P.useState([]),{setCurrentCategory:s,getRelativeTime:l}=Pe(),o=nn(t),u=bt(o),h=b=>b?{SAĞLIK:"Sağlık",ASAYIŞ:"Asayiş",ÇEVRE:"Çevre",POLİTİKA:"Politika",EĞİTİM:"Eğitim",EKONOMİ:"Ekonomi","KÜLTÜR SANAT":"Kültür Sanat","BİLİM VE TEKNOLOJİ":"Bilim ve Teknoloji",MAGAZİN:"Magazin","DIŞ HABER":"Dünya",DÜNYA:"Dünya"}[b]||b.split(" ").map(V=>V.charAt(0).toUpperCase()+V.slice(1).toLowerCase()).join(" "):"",{data:m,isLoading:f,error:N,refetch:v}=ye(["category-news",o,a],()=>u==="DÜNYA"?fe.getWorldNews({page:a,limit:30}):fe.getNews({kategori:o,page:a,limit:30}),{enabled:!!u,keepPreviousData:!1,staleTime:0,cacheTime:0,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:2,onError:b=>{console.error("Kategori haberleri yüklenirken hata:",b),n([])}});P.useEffect(()=>(s(u),i(1),n([]),()=>s("")),[t,u,s]),P.useEffect(()=>{m!=null&&m.success&&Array.isArray(m.data)&&n(a===1?m.data:b=>[...b,...m.data])},[m,a]);const d=((S=m==null?void 0:m.pagination)==null?void 0:S.hasMore)||!1,w=()=>{!f&&d&&i(b=>b+1)},A=!u||f&&a===1;return N?e("div",{className:"container",children:e(et,{message:"Haberler yüklenirken bir hata oluştu.",onRetry:()=>{v()}})}):c(ie,{children:[c(Ee,{children:[c("title",{children:[u," Haberleri - Meta Analiz Haber"]}),e("meta",{name:"description",content:`${u} kategorisindeki en güncel haberler. Meta Analiz Haber'de ${u} ile ilgili tüm gelişmeleri takip edin.`})]}),c("div",{className:"category-page",children:[e("div",{className:"page-header",children:c("div",{className:"container",children:[c("h1",{className:"page-title",children:[e("span",{className:"category-name",children:h(u)}),e("span",{className:"category-suffix",children:u==="GENEL"?" Haberler":" Haberleri"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:c("div",{style:{fontSize:"14px",marginBottom:0,display:"flex",alignItems:"center",gap:"6px",fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:[e(z,{to:"/",style:{textDecoration:"none",color:"#666",fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Ana Sayfa"}),e("span",{style:{color:"#999",fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:">"}),e("span",{style:{textTransform:"capitalize",color:"#333",fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:h(u)})]})})})]})}),e("div",{className:"category-news-section",children:c("div",{className:"container",children:[A?e("div",{className:"text-center py-5",children:e(he,{text:"Haberler yükleniyor..."})}):f&&r.length===0?e("div",{className:"text-center py-5",children:e(he,{text:"Haberler yükleniyor..."})}):r.length===0&&!f&&u&&m?c("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Bu kategoride henüz haber bulunmuyor."]}):u==="EKONOMİ"?e("div",{className:"economy-category-layout",children:c("div",{className:"economy-content-wrapper",children:[e("div",{className:"economy-news-grid",children:r.map(b=>e("article",{className:"news-card",children:c(z,{to:`/haber/${b.slug||b.haber_kodu}`,children:[c("div",{className:"card-image",children:[e("img",{src:b.main_image||b.video_poster_url||"/assets/images/placeholder.svg",alt:b.title,onError:R=>{R.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"card-category",children:b.kategori||"Ekonomi"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:b.title}),e("div",{className:"card-date",children:l(b.pub_date)})]})]})},b.haber_kodu))}),e("div",{className:"economy-sidebar",children:c("div",{className:"economy-widgets-stack",children:[e(Pr,{}),e(yn,{}),e(Ar,{})]})})]})}):u==="DÜNYA"||u==="DIŞ HABER"?c("div",{className:"world-category-layout",children:[r.filter(b=>b.main_image&&b.main_image!=="/assets/images/placeholder.svg"&&b.main_image.trim()!=="").length>0&&e("div",{className:"world-news-grid",children:r.filter(b=>b.main_image&&b.main_image!=="/assets/images/placeholder.svg"&&b.main_image.trim()!=="").map(b=>e("article",{className:"news-card",children:c(z,{to:`/haber/${b.slug||b.haber_kodu}`,children:[e("div",{className:"card-image",children:e("img",{src:b.main_image||b.video_poster_url||"/assets/images/placeholder.svg",alt:b.title,onError:R=>{R.target.src="/assets/images/placeholder.svg"}})}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:b.title}),e("div",{className:"card-date",children:l(b.pub_date)})]})]})},b.haber_kodu))}),r.filter(b=>!b.main_image||b.main_image==="/assets/images/placeholder.svg"||b.main_image.trim()==="").length>0&&c("div",{className:"world-news-list",children:[e("h4",{className:"world-list-title",children:"Diğer Dünya Haberleri"}),r.filter(b=>!b.main_image||b.main_image==="/assets/images/placeholder.svg"||b.main_image.trim()==="").map(b=>e("div",{className:"world-list-item",children:c(z,{to:`/haber/${b.slug||b.haber_kodu}`,className:"world-list-content",children:[e("h4",{className:"world-list-title-text",children:b.title}),e("div",{className:"world-list-meta",children:e("span",{className:"world-list-date",children:l(b.pub_date)})})]})},b.haber_kodu))]})]}):e("div",{className:"category-news-grid",children:r.map(b=>e("article",{className:"news-card",children:c(z,{to:`/haber/${b.slug||b.haber_kodu}`,children:[c("div",{className:"card-image",children:[e("img",{src:b.main_image||b.video_poster_url||"/assets/images/placeholder.svg",alt:b.title,onError:R=>{R.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"card-category",children:b.kategori||"Haber"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:b.title}),e("div",{className:"card-date",children:l(b.pub_date)})]})]})},b.haber_kodu))}),d&&e("div",{className:"text-center mt-5",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:w,disabled:f,children:f?c(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Daha Fazla Haber Yükleniyor..."]}):"Daha Fazla Haber Gör"})}),f&&a>1&&e("div",{className:"text-center py-3",children:e(he,{})})]})})]})]})},Rr=({news:t,variant:a="default",showCategory:i=!0,showDate:r=!0,className:n=""})=>{const{getRelativeTime:s}=Pe();if(!t)return null;const l=()=>{let f="news-card";switch(a){case"large":f+=" news-card--large";break;case"compact":f+=" news-card--compact";break;case"video":f+=" news-card--video";break;case"local":f+=" news-card--local";break;case"world":f+=" news-card--world";break}return n&&(f+=` ${n}`),f},o=()=>t.kategori?t.kategori:t.sehir?t.sehir:"Genel",u=f=>{f.target.src="/assets/images/placeholder.svg"},h=()=>t.main_image&&t.main_image!=="/assets/images/placeholder.svg"&&t.main_image.trim()!==""?t.main_image:t.kategori==="DÜNYA"&&t.video_poster_url&&t.video_poster_url.trim()!==""?t.video_poster_url:t.featured_image||"/assets/images/placeholder.svg",m=()=>t.slug&&t.slug.trim()!==""?`/haber/${t.slug}`:`/haber/${t.haber_kodu}`;return e("article",{className:l(),children:c(z,{to:m(),children:[c("div",{className:"news-card__image",children:[e(Me.LazyLoadImage,{src:h(),alt:Ce(Se(t.title)),effect:"blur",onError:u,placeholderSrc:"/assets/images/placeholder.svg"}),i&&e("span",{className:"news-card__category",children:o()}),t.son_dakika==="Evet"&&e("span",{className:"news-card__breaking",children:"SON DAKİKA"})]}),c("div",{className:"news-card__content",children:[e("h3",{className:"news-card__title",children:Ce(Se(t.title))}),r&&e("div",{className:"news-card__meta",children:e("span",{className:"news-card__date",children:t.formatted_date||s(t.pub_date||t.tarih)})})]})]})})};Rr.propTypes={news:M.shape({haber_kodu:M.string.isRequired,title:M.string.isRequired,slug:M.string,main_image:M.string,featured_image:M.string,video_poster_url:M.string,kategori:M.string,sehir:M.string,pub_date:M.string,tarih:M.string,formatted_date:M.string,son_dakika:M.string}).isRequired,variant:M.oneOf(["default","large","compact","video","local","world"]),showCategory:M.bool,showDate:M.bool,className:M.string};const Tt=({news:t=[],showCategory:a=!1,variant:i="default"})=>!t||t.length===0?e("div",{className:"alert alert-info",children:"Henüz haber bulunmuyor."}):e("div",{className:"news-grid",children:t.map(r=>e("div",{className:"news-grid-item",children:e(Rr,{news:r,variant:i,showCategory:a,showDate:!0})},r.haber_kodu))});Tt.propTypes={news:M.arrayOf(M.shape({haber_kodu:M.string.isRequired,title:M.string.isRequired,featured_image:M.string,kategori:M.string,sehir:M.string,pub_date:M.string,son_dakika:M.string})),showCategory:M.bool,variant:M.string};const vn=()=>{var v;const[t,a]=P.useState(1),[i,r]=P.useState([]),{setCurrentCategory:n}=Pe();oe.useEffect(()=>(n("Video Haberler"),()=>n("")),[n]);const{data:s,isLoading:l,error:o,refetch:u}=ye(["video-news",t],()=>fe.getNews({limit:12,page:t,has_videos:!0}),{keepPreviousData:!0,onSuccess:d=>{var w;r(t===1?((w=d==null?void 0:d.data)==null?void 0:w.data)||[]:A=>{var S;return[...A,...((S=d==null?void 0:d.data)==null?void 0:S.data)||[]]})}});if(o)return e("div",{className:"container py-5",children:e(et,{message:"Videolu haberler yüklenirken bir hata oluştu.",onRetry:u})});const h=((v=s==null?void 0:s.data)==null?void 0:v.total)||0,m=Math.ceil(h/12),f=t<m;return c(ie,{children:[c(Ee,{children:[e("title",{children:"Videolu Haberler - Meta Analiz Haber"}),e("meta",{name:"description",content:"Video içeren güncel haberler ve gelişmeler."})]}),e("div",{className:"page-header",children:c("div",{className:"container",children:[c("h1",{className:"page-title",children:[e("i",{className:"fas fa-play-circle me-3"}),"Videolu Haberler"]}),e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:[e("li",{className:"breadcrumb-item",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:e(z,{to:"/",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active","aria-current":"page",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Videolu Haberler"})]})})]})}),e("div",{className:"container",children:e("div",{className:"category-news-section",children:l&&t===1?e("div",{className:"text-center py-5",children:e(he,{})}):c(ie,{children:[e(Tt,{news:i}),f&&e("div",{className:"text-center mt-4",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:()=>{f&&!l&&a(d=>d+1)},disabled:l,children:l?c(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Video Yükle"})}),l&&t>1&&e("div",{className:"text-center py-3",children:e(he,{})}),i.length===0&&!l&&e("div",{className:"text-center py-5",children:c("div",{className:"no-results",children:[e("i",{className:"fas fa-video fa-3x text-muted mb-3"}),e("h3",{children:"Videolu haber bulunamadı"}),e("p",{className:"text-muted",children:"Şu anda videolu haber bulunmamaktadır."}),e(z,{to:"/",className:"btn btn-primary",children:"Ana Sayfaya Dön"})]})})]})})})]})},bn=()=>{var v;const[t,a]=P.useState(1),[i,r]=P.useState([]),{setCurrentCategory:n}=Pe();oe.useEffect(()=>(n("Son Haberler"),()=>n("")),[n]);const{data:s,isLoading:l,error:o,refetch:u}=ye(["latest-news",t],()=>fe.getNews({limit:12,page:t,sort:"pub_date",order:"DESC"}),{keepPreviousData:!0,onSuccess:d=>{const w=d!=null&&d.success&&Array.isArray(d.data)?d.data:[];r(t===1?w:A=>[...A,...w])}});if(o)return e("div",{className:"container py-5",children:e(et,{message:"Son haberler yüklenirken bir hata oluştu.",onRetry:u})});const h=((v=s==null?void 0:s.data)==null?void 0:v.total)||0,m=Math.ceil(h/12),f=t<m;return c(ie,{children:[c(Ee,{children:[e("title",{children:"Son Haberler - Meta Analiz Haber"}),e("meta",{name:"description",content:"En güncel haberler ve son dakika gelişmeleri."})]}),e("div",{className:"page-header",children:c("div",{className:"container",children:[c("h1",{className:"page-title",children:[e("i",{className:"fas fa-clock me-3"}),"Son Haberler"]}),e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(z,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active","aria-current":"page",children:"Son Haberler"})]})})]})}),e("div",{className:"container",children:e("div",{className:"category-news-section",children:l&&t===1?e("div",{className:"text-center py-5",children:e(he,{})}):c(ie,{children:[e(Tt,{news:i}),f&&e("div",{className:"text-center mt-4",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:()=>{f&&!l&&a(d=>d+1)},disabled:l,children:l?c(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Haber Yükle"})}),l&&t>1&&e("div",{className:"text-center py-3",children:e(he,{})}),i.length===0&&!l&&e("div",{className:"text-center py-5",children:c("div",{className:"no-results",children:[e("i",{className:"fas fa-newspaper fa-3x text-muted mb-3"}),e("h3",{children:"Haber bulunamadı"}),e("p",{className:"text-muted",children:"Şu anda haber bulunmamaktadır."}),e(z,{to:"/",className:"btn btn-primary",children:"Ana Sayfaya Dön"})]})})]})})})]})};const Nn=()=>{const[t,a]=P.useState(null),[i,r]=P.useState(null),[n,s]=P.useState(!0),[l,o]=P.useState(null),[u,h]=P.useState(null),m="http://localhost/metaanalizhaber_yeni/backend",f=async()=>{try{const A=await(await fetch(`${m}/cron_monitor.php?action=status`)).json();if(A.success)a(A.data),h(new Date);else throw new Error(A.error||"Veri alınamadı")}catch(w){o("Cron durumu alınamadı: "+w.message)}},N=async()=>{try{const A=await(await fetch(`${m}/cron_monitor.php?action=rss_stats`)).json();if(A.success)r(A.data);else throw new Error(A.error||"RSS istatistikleri alınamadı")}catch(w){console.error("RSS stats error:",w)}};P.useEffect(()=>{(async()=>{s(!0),o(null),await Promise.all([f(),N()]),s(!1)})()},[]),P.useEffect(()=>{const w=setInterval(()=>{f(),N()},3e4);return()=>clearInterval(w)},[]);const v=({status:w})=>{const S=(b=>{switch(b){case"running":return{class:"success",text:"Çalışıyor",icon:"fa-check-circle"};case"delayed":return{class:"warning",text:"Gecikmeli",icon:"fa-exclamation-triangle"};case"stopped":return{class:"danger",text:"Durdu",icon:"fa-times-circle"};default:return{class:"secondary",text:"Bilinmiyor",icon:"fa-question-circle"}}})(w);return c("span",{className:`badge badge-${S.class}`,children:[e("i",{className:`fas ${S.icon} me-1`}),S.text]})},d=({score:w})=>c("div",{className:`health-score health-${(S=>S>=80?"success":S>=60?"warning":"danger")(w)}`,children:[e("div",{className:"health-circle",children:e("span",{className:"health-number",children:w})}),e("small",{children:"Sağlık Skoru"})]});return n?e("div",{className:"container py-5",children:c("div",{className:"text-center",children:[e(he,{}),e("p",{className:"mt-3",children:"Cron durumu yükleniyor..."})]})}):l?e("div",{className:"container py-5",children:e(et,{message:l,onRetry:()=>window.location.reload()})}):c(ie,{children:[c(Ee,{children:[e("title",{children:"Cron Monitoring - Meta Analiz Haber"}),e("meta",{name:"description",content:"Cron job'ların durumu ve sistem istatistikleri"})]}),e("div",{className:"page-header",children:c("div",{className:"container",children:[c("h1",{className:"page-title",children:[e("i",{className:"fas fa-cogs me-3"}),"Cron Monitoring"]}),e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(z,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active","aria-current":"page",children:"Cron Monitoring"})]})}),u&&c("p",{className:"text-muted mb-0",children:[e("i",{className:"fas fa-sync-alt me-1"}),"Son güncelleme: ",u.toLocaleTimeString("tr-TR")]})]})}),c("div",{className:"container",children:[e("div",{className:"row mb-4",children:e("div",{className:"col-12",children:e("div",{className:"card",children:c("div",{className:"card-body text-center",children:[e("h5",{className:"card-title",children:"Genel Sistem Durumu"}),c("div",{className:`overall-health health-${(t==null?void 0:t.overall_health)||"unknown"}`,children:[e("i",{className:`fas ${(t==null?void 0:t.overall_health)==="good"?"fa-check-circle":(t==null?void 0:t.overall_health)==="warning"?"fa-exclamation-triangle":"fa-times-circle"} fa-3x mb-2`}),e("h4",{children:(t==null?void 0:t.overall_health)==="good"?"İyi":(t==null?void 0:t.overall_health)==="warning"?"Uyarı":"Kritik"})]})]})})})}),c("div",{className:"row",children:[(t==null?void 0:t.news_fetch)&&e("div",{className:"col-lg-4 mb-4",children:c("div",{className:"card h-100",children:[c("div",{className:"card-header d-flex justify-content-between align-items-center",children:[c("h6",{className:"mb-0",children:[e("i",{className:"fas fa-newspaper me-2"}),t.news_fetch.name]}),e(v,{status:t.news_fetch.status})]}),c("div",{className:"card-body",children:[c("div",{className:"row",children:[e("div",{className:"col-8",children:c("div",{className:"stats-grid",children:[c("div",{className:"stat-item",children:[e("span",{className:"stat-number",children:t.news_fetch.news_count_24h}),e("span",{className:"stat-label",children:"Haber (24s)"})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number",children:t.news_fetch.image_count_24h}),e("span",{className:"stat-label",children:"Görsel (24s)"})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number",children:t.news_fetch.video_count_24h}),e("span",{className:"stat-label",children:"Video (24s)"})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:t.news_fetch.error_count_24h}),e("span",{className:"stat-label",children:"Hata (24s)"})]})]})}),e("div",{className:"col-4 text-center",children:e(d,{score:t.news_fetch.health_score})})]}),t.news_fetch.last_run&&e("div",{className:"mt-3",children:c("small",{className:"text-muted",children:[e("i",{className:"fas fa-clock me-1"}),"Son çalışma: ",t.news_fetch.last_run.ago]})})]})]})}),(t==null?void 0:t.media_download)&&e("div",{className:"col-lg-4 mb-4",children:c("div",{className:"card h-100",children:[c("div",{className:"card-header d-flex justify-content-between align-items-center",children:[c("h6",{className:"mb-0",children:[e("i",{className:"fas fa-download me-2"}),t.media_download.name]}),e(v,{status:t.media_download.status})]}),c("div",{className:"card-body",children:[c("div",{className:"row",children:[e("div",{className:"col-8",children:c("div",{className:"stats-grid",children:[c("div",{className:"stat-item",children:[e("span",{className:"stat-number text-warning",children:t.media_download.pending_images}),e("span",{className:"stat-label",children:"Bekleyen Görsel"})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number text-warning",children:t.media_download.pending_videos}),e("span",{className:"stat-label",children:"Bekleyen Video"})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:t.media_download.failed_images}),e("span",{className:"stat-label",children:"Başarısız Görsel"})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:t.media_download.failed_videos}),e("span",{className:"stat-label",children:"Başarısız Video"})]})]})}),e("div",{className:"col-4 text-center",children:e(d,{score:t.media_download.health_score})})]}),t.media_download.last_run&&e("div",{className:"mt-3",children:c("small",{className:"text-muted",children:[e("i",{className:"fas fa-clock me-1"}),"Son çalışma: ",t.media_download.last_run.ago]})})]})]})}),(t==null?void 0:t.system_cleanup)&&e("div",{className:"col-lg-4 mb-4",children:c("div",{className:"card h-100",children:[c("div",{className:"card-header d-flex justify-content-between align-items-center",children:[c("h6",{className:"mb-0",children:[e("i",{className:"fas fa-broom me-2"}),t.system_cleanup.name]}),e(v,{status:t.system_cleanup.status})]}),c("div",{className:"card-body",children:[c("div",{className:"row",children:[c("div",{className:"col-8",children:[t.system_cleanup.disk_usage&&c("div",{className:"mb-3",children:[c("div",{className:"d-flex justify-content-between",children:[e("span",{children:"Disk Kullanımı"}),c("span",{children:[t.system_cleanup.disk_usage.usage_percent,"%"]})]}),e("div",{className:"progress",children:e("div",{className:`progress-bar ${t.system_cleanup.disk_usage.usage_percent>80?"bg-danger":t.system_cleanup.disk_usage.usage_percent>60?"bg-warning":"bg-success"}`,style:{width:`${t.system_cleanup.disk_usage.usage_percent}%`}})}),c("small",{className:"text-muted",children:[t.system_cleanup.disk_usage.used," / ",t.system_cleanup.disk_usage.total]})]}),c("div",{className:"stat-item",children:[e("span",{className:"stat-number text-danger",children:t.system_cleanup.error_count_7d}),e("span",{className:"stat-label",children:"Hata (7 gün)"})]})]}),e("div",{className:"col-4 text-center",children:e(d,{score:t.system_cleanup.health_score})})]}),t.system_cleanup.last_run&&e("div",{className:"mt-3",children:c("small",{className:"text-muted",children:[e("i",{className:"fas fa-clock me-1"}),"Son çalışma: ",t.system_cleanup.last_run.ago]})})]})]})})]}),i&&e("div",{className:"row mt-4",children:e("div",{className:"col-12",children:c("div",{className:"card",children:[e("div",{className:"card-header",children:c("h6",{className:"mb-0",children:[e("i",{className:"fas fa-rss me-2"}),"RSS İstatistikleri (Son 24 Saat)"]})}),e("div",{className:"card-body",children:c("div",{className:"row",children:[e("div",{className:"col-md-2",children:c("div",{className:"stat-item text-center",children:[e("span",{className:"stat-number",children:i.total_requests_24h}),e("span",{className:"stat-label",children:"Toplam İstek"})]})}),e("div",{className:"col-md-2",children:c("div",{className:"stat-item text-center",children:[e("span",{className:"stat-number text-success",children:i.successful_requests_24h}),e("span",{className:"stat-label",children:"Başarılı"})]})}),e("div",{className:"col-md-2",children:c("div",{className:"stat-item text-center",children:[e("span",{className:"stat-number text-danger",children:i.failed_requests_24h}),e("span",{className:"stat-label",children:"Başarısız"})]})}),e("div",{className:"col-md-2",children:c("div",{className:"stat-item text-center",children:[c("span",{className:"stat-number",children:[i.success_rate,"%"]}),e("span",{className:"stat-label",children:"Başarı Oranı"})]})}),e("div",{className:"col-md-2",children:c("div",{className:"stat-item text-center",children:[c("span",{className:"stat-number",children:[i.avg_response_time,"s"]}),e("span",{className:"stat-label",children:"Ort. Yanıt Süresi"})]})}),e("div",{className:"col-md-2",children:e("div",{className:"stat-item text-center",children:c("button",{className:"btn btn-sm btn-outline-primary",onClick:()=>{f(),N()},children:[e("i",{className:"fas fa-sync-alt"}),"Yenile"]})})})]})})]})})})]})]})},wn=({category:t,excludeHaberKodu:a})=>{const{getRelativeTime:i}=Pe(),{data:r,isLoading:n}=ye(["related-news",t,a],()=>fe.getNews({kategori:t,limit:6}),{enabled:!!t,select:s=>{if(s.success&&s.data){console.log("RelatedNews - Original data:",s.data.length,"items"),console.log("RelatedNews - Excluding haber_kodu:",a);const l=s.data.filter(o=>{var m;const u=o.haber_kodu===a,h=o.main_image&&o.main_image!=="/assets/images/placeholder.svg"&&o.main_image.trim()!=="";return console.log("RelatedNews - Item:",o.haber_kodu,{title:(m=o.title)==null?void 0:m.substring(0,50),main_image:o.main_image,featured_image:o.featured_image,hasImage:h,shouldExclude:u}),u&&console.log("RelatedNews - Excluding item:",o.haber_kodu,o.title),!u&&h});return console.log("RelatedNews - Filtered data:",l.length,"items"),{...s,data:l}}return s}});return n?c("section",{className:"related-news mt-5",children:[e("div",{className:"section-title",children:e("h2",{children:"İlgili Haberler"})}),e("div",{className:"text-center py-3",children:e(he,{})})]}):!(r!=null&&r.success)||!Array.isArray(r.data)||r.data.length===0?null:c("section",{className:"related-news mt-5",children:[e("div",{className:"section-header",children:e("h2",{className:"section-title",children:"İlgili Haberler"})}),e("div",{className:"related-news-grid",children:r.data.slice(0,3).map(s=>{var l;return e("article",{className:"related-news-item",children:c("a",{href:`/haber/${s.slug||s.haber_kodu}`,className:"related-news-link",children:[c("div",{className:"related-news-image-container",children:[e("img",{src:s.main_image||s.featured_image||"/assets/images/placeholder.svg",alt:s.title,className:"related-news-image",onError:o=>{o.target.src="/assets/images/placeholder.svg"}}),s.son_dakika==="Evet"&&e("span",{className:"breaking-badge",children:"SON DAKİKA"})]}),c("div",{className:"related-news-content",children:[e("h3",{className:"related-news-title",children:(l=s.title)==null?void 0:l.replace(/<[^>]*>/g,"")}),e("div",{className:"related-news-meta",children:c("span",{className:"related-news-date",children:[e("i",{className:"far fa-clock"}),s.formatted_date||i(s.pub_date)]})})]})]})},s.haber_kodu)})})]})},kn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAETSURBVHgB7ZZhDYMwEIWPKUACEpCAg+FgSNgczMIcbA46B8wBEiYBB901O5IuuWvWFvqH+5Km4Y4rj3stAUBRFGVHVFLCWtvi1DOpO46Wxu9iVXX16muczky9wfsmiAEXGyxPF8p59Y1wzyA98wBpGBwzE+8hgyQx2GYnZGRSJ8ggtTOOGxOrfatiyRHjNiFn1dKdGUqJIaseTKp3J4nyUYJyOuMwTMwd6WUjlxODbz9C2KoXlBJDcFa19NGL+ritIUayaoDSYgJWHUE+cduIITirOppH+JO1xBghPgAvlGUVMWQVtz+cVS7nWzVvKoa4MLEOvpv5SdcTCpe6mPY/gwu+hZqGamovvDy8CQlRFEXZHR+Zz49QLme6EwAAAABJRU5ErkJggg==";const _n=()=>{var W,te,G,X;const{newsSlug:t}=jt();zr();const{formatDate:a}=Pe(),[i,r]=P.useState(()=>{const y=localStorage.getItem("news-font-size");return y?parseInt(y):18}),[n,s]=P.useState(!1),l=()=>{const y=Math.min(i+2,24);r(y),localStorage.setItem("news-font-size",y.toString())},o=()=>{const y=Math.max(i-2,14);r(y),localStorage.setItem("news-font-size",y.toString())},u=()=>{r(18),localStorage.setItem("news-font-size","18")},h=()=>{if(n)window.speechSynthesis.cancel(),s(!1);else if("speechSynthesis"in window&&d){const y=We(d.description),C=`${d.title}. ${y}`,E=new SpeechSynthesisUtterance(C);E.lang="tr-TR",E.rate=.9,E.pitch=1,E.onstart=()=>s(!0),E.onend=()=>s(!1),E.onerror=()=>s(!1),window.speechSynthesis.speak(E)}},{data:m,isLoading:f,error:N,refetch:v}=ye(["news-detail",t],()=>fe.getNewsDetail(t),{enabled:!!t,onError:y=>{var C,E;console.error("Haber detay hatası:",y),console.error("Error details:",(C=y.response)==null?void 0:C.data),console.error("Error status:",(E=y.response)==null?void 0:E.status)}}),d=m!=null&&m.success?m.data:null,w=((W=m==null?void 0:m.data)==null?void 0:W.images)||[],A=((te=m==null?void 0:m.data)==null?void 0:te.videos)||[],S=(y,C)=>{if(!y||C.length<=1)return{contentElements:We(y||"").split(`
`).filter(ve=>ve.trim()).map((ve,Ae)=>({type:"paragraph",content:ve,index:Ae})),usedImages:[]};const U=We(y).split(`
`).filter(ne=>ne.trim()),B=[],Z=[];C[0];const q=C.slice(1);if(q.length===0)return{contentElements:U.map((ee,ce)=>({type:"paragraph",content:ee,index:ce})),usedImages:[]};const ae=Math.max(2,Math.floor(U.length/q.length));return U.forEach((ne,ee)=>{if(B.push({type:"paragraph",content:ne,index:ee}),q.length>0&&ee>0&&(ee+1)%ae===0&&ee<U.length-1){const ce=q.shift();ce&&(B.push({type:"image",content:ce,index:Z.length}),Z.push(ce))}}),{contentElements:B,usedImages:Z}},{contentElements:b,usedImages:R}=d?S(d.description,w):{contentElements:[],usedImages:[]};P.useEffect(()=>{var y,C;d&&(console.log("News category:",d.kategori),console.log("News ust_kategori:",d.ust_kategori),console.log("News description:",d.description),console.log("News description type:",typeof d.description),console.log("News description length:",(y=d.description)==null?void 0:y.length),console.log("Content elements:",b),console.log("Content elements length:",b.length),((C=d.kategori)!=null&&C.includes("DÜNYA")||d.ust_kategori==="DIŞ HABER")&&(console.log("=== DÜNYA HABERİ DEBUG ==="),console.log("Raw description:",d.description),console.log("Cleaned content:",We(d.description||"")),console.log("=========================")))},[d,b]);const V=w.length>0?[w[0],...w.slice(1).filter(y=>!R.includes(y))]:[],[j,D]=P.useState(0),le=()=>{V.length>1&&D(y=>(y+1)%V.length)},H=()=>{V.length>1&&D(y=>(y-1+V.length)%V.length)},Y=y=>{D(y)};return P.useEffect(()=>{if(d!=null&&d.title){const y=Ce(d.title);document.title=`${y} - Meta Analiz Haber`}},[d,w]),f?e("div",{className:"container",children:e("div",{className:"text-center py-5",children:e(he,{})})}):N?e("div",{className:"container",children:c("div",{className:"alert alert-danger",children:[e("h4",{children:"Haber yüklenirken bir hata oluştu"}),c("p",{children:[e("strong",{children:"Slug:"})," ",t]}),c("p",{children:[e("strong",{children:"Hata:"})," ",N.message]}),c("p",{children:[e("strong",{children:"Status:"})," ",(G=N.response)==null?void 0:G.status]}),c("p",{children:[e("strong",{children:"Response:"})," ",JSON.stringify((X=N.response)==null?void 0:X.data)]}),e("button",{className:"btn btn-primary",onClick:v,children:"Tekrar Dene"})]})}):d?e(ie,{children:e("div",{className:"news-detail-page",children:c("div",{className:"container",children:[c("article",{className:"news-article",children:[c("header",{className:"news-header",children:[d.son_dakika==="Evet"&&e("span",{className:"breaking-badge",children:"SON DAKİKA"}),e("h1",{className:"news-title",children:Ce(d.title)}),c("div",{className:"news-meta",children:[c("div",{className:"news-meta-left",children:[e("span",{className:"category-badge",children:d.kategori}),c("span",{className:"news-date",children:[e("i",{className:"far fa-clock"}),a(d.pub_date)]}),c("span",{className:"news-location",children:[e("i",{className:"fas fa-map-marker-alt"}),d.sehir]})]}),c("div",{className:"news-meta-right",children:[c("div",{className:"font-controls",children:[e("button",{onClick:o,className:"font-control-btn",title:"Yazıyı küçült",children:"A-"}),e("button",{onClick:u,className:"font-control-btn active",title:"Normal boyut",children:"A"}),e("button",{onClick:l,className:"font-control-btn",title:"Yazıyı büyült",children:"A+"})]}),c("button",{onClick:h,className:`speech-btn ${n?"reading":""}`,title:n?"Okumayı durdur":"Sesli oku",children:[e("i",{className:`fas ${n?"fa-stop":"fa-volume-up"}`}),n?"Durdur":"Sesli Oku"]})]})]})]}),(V.length>0||d.main_image)&&e("div",{className:"news-image-carousel",children:c("div",{className:"carousel-container",children:[c("div",{className:"carousel-main",children:[V.length>0?e("img",{src:V[j],alt:d.title,className:"main-image"}):e("img",{src:d.main_image,alt:d.title,className:"main-image"}),V.length>1&&c(ie,{children:[e("button",{className:"carousel-arrow carousel-arrow-left",onClick:H,"aria-label":"Önceki görsel",children:e("i",{className:"fas fa-chevron-left"})}),e("button",{className:"carousel-arrow carousel-arrow-right",onClick:le,"aria-label":"Sonraki görsel",children:e("i",{className:"fas fa-chevron-right"})})]})]}),V.length>1&&e("div",{className:"carousel-thumbnails",children:V.map((y,C)=>e("button",{className:`thumbnail ${C===j?"active":""}`,onClick:()=>Y(C),children:e("img",{src:y,alt:`Görsel ${C+1}`})},C))})]})}),c("div",{className:"news-content",children:[e("h2",{className:"content-title",children:"Haber İçeriği"}),e("div",{className:"content-text",children:(()=>{if(b.length>0)return b.map((y,C)=>y.type==="paragraph"?e("p",{className:"content-paragraph",style:{fontSize:`${i}px`},children:y.content},`paragraph-${C}`):y.type==="image"?e("div",{className:"content-image",children:e("img",{src:y.content,alt:`Haber görseli ${y.index+1}`,className:"inline-image",loading:"lazy"})},`image-${C}`):null);if(d!=null&&d.description&&d.description.trim()){const y=We(d.description);if(y&&y.trim())return e("div",{className:"content-paragraph",style:{fontSize:`${i}px`},dangerouslySetInnerHTML:{__html:y.replace(/\n/g,"<br/>")}})}return d!=null&&d.description&&d.description.trim()?e("div",{className:"content-paragraph",style:{fontSize:`${i}px`},dangerouslySetInnerHTML:{__html:d.description.replace(/\n/g,"<br/>")}}):d!=null&&d.title?c("div",{className:"content-paragraph",style:{fontSize:`${i}px`},children:[c("p",{children:[e("strong",{children:"Haber Başlığı:"})," ",Ce(d.title)]}),e("p",{className:"text-muted",children:"Bu haber için detaylı içerik henüz işlenebilir durumda değil. Lütfen daha sonra tekrar deneyin."})]}):c("div",{className:"alert alert-info",children:[e("i",{className:"fas fa-info-circle me-2"}),"Bu haber için detaylı içerik henüz mevcut değil."]})})()}),A.length>0&&e("div",{className:"news-videos",children:A.map(y=>{var U,B;const C=(U=y.video_url)==null?void 0:U.replace(/&amp;/g,"&"),E=(B=y.poster_url)==null?void 0:B.replace(/&amp;/g,"&");return console.log("Video data:",{video_kodu:y.video_kodu,videoUrl:C,posterUrl:E,description:y.description,hasVideoUrl:!!C,hasPosterUrl:!!E}),C?c("div",{className:"video-container",children:[c("video",{controls:!0,poster:E,className:"video-player",preload:"metadata",onError:Z=>{console.error("Video yükleme hatası:",Z.target.error),console.log("Video URL:",C),console.log("Poster URL:",E)},onLoadStart:()=>{console.log("Video yüklenmeye başladı:",C)},onCanPlay:()=>{console.log("Video oynatılabilir:",C)},onLoadedMetadata:()=>{console.log("Video metadata yüklendi:",C)},children:[e("source",{src:C,type:"video/mp4"}),e("source",{src:C,type:"video/webm"}),e("source",{src:C,type:"video/ogg"}),"Tarayıcınız video etiketini desteklemiyor."]}),y.description&&e("div",{className:"video-caption",children:y.description})]},y.video_kodu||y.video_url||Math.random()):(console.warn("Video URL bulunamadı, video atlanıyor"),null)})}),e("div",{className:"news-disclaimer",children:e("p",{className:"disclaimer-text",children:'"Bu içerik IHA Haber Ajansı tarafından hazırlanmıştır. İçerikte yer alan görüş ve ifadeler, editöryal politikamızı yansıtmayabilir"'})})]})]}),c("footer",{className:"news-footer",children:[e("div",{className:"news-tags",children:c("div",{className:"tags-container",children:[e("h4",{className:"tags-title",children:"Etiketler:"}),c("div",{className:"tags-list",children:[e(z,{to:`/kategori/${encodeURIComponent(d.kategori)}`,className:"tag-item",children:d.kategori}),e(z,{to:`/arama?q=${encodeURIComponent(d.sehir)}`,className:"tag-item",children:d.sehir})]})]})}),e("div",{className:"news-share",children:c("div",{className:"share-container",children:[e("h4",{className:"share-title",children:"Paylaş:"}),c("div",{className:"share-buttons",children:[e("a",{href:`https://sosyal.teknofest.app/share?text=${encodeURIComponent(`${d.title} #${d.kategori||"Haber"} #metaanalizhaber`)}&url=${encodeURIComponent(window.location.href)}`,target:"_blank",rel:"noopener noreferrer",className:"share-button next-sosyal","aria-label":"Next Sosyal'de paylaş",title:"Next Sosyal'de paylaş",children:e("img",{src:kn,alt:"Next Sosyal",width:"26",height:"26",style:{marginTop:"-1px"}})}),e("a",{href:`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href+(window.location.href.includes("?")?"&":"?")+"fbref="+Date.now())}`,target:"_blank",rel:"noopener noreferrer",className:"share-button facebook","aria-label":"Facebook'ta paylaş",title:"Facebook'ta paylaş",children:e("i",{className:"fab fa-facebook-f"})}),e("a",{href:`https://x.com/intent/post?text=${encodeURIComponent(d.title)}&url=${encodeURIComponent(window.location.href.split("?")[0])}`,target:"_blank",rel:"noopener noreferrer",className:"share-button twitter","aria-label":"X'te paylaş",title:"X'te paylaş",children:e("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})})}),e("a",{href:`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(d.title)}&summary=${encodeURIComponent(Se(d.description).substring(0,200))}&source=MetaAnaliz%20Haber`,target:"_blank",rel:"noopener noreferrer",className:"share-button linkedin","aria-label":"LinkedIn'de paylaş",title:"LinkedIn'de paylaş",children:e("i",{className:"fab fa-linkedin-in"})}),e("a",{href:`https://wa.me/?text=${encodeURIComponent(`${d.title}

${Se(d.description).substring(0,100)}...

Detaylar: ${window.location.href}`)}`,target:"_blank",rel:"noopener noreferrer",className:"share-button whatsapp","aria-label":"WhatsApp'ta paylaş",title:"WhatsApp'ta paylaş",children:e("i",{className:"fab fa-whatsapp"})}),e("a",{href:`https://telegram.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(d.title)}`,target:"_blank",rel:"noopener noreferrer",className:"share-button telegram","aria-label":"Telegram'da paylaş",title:"Telegram'da paylaş",children:e("i",{className:"fab fa-telegram-plane"})}),e("button",{onClick:async y=>{try{if(navigator.share)await navigator.share({title:d.title,text:Se(d.description).substring(0,100)+"...",url:window.location.href});else if(navigator.clipboard){await navigator.clipboard.writeText(window.location.href);const C=y.target.closest("button"),E=C.innerHTML;C.innerHTML='<i class="fas fa-check"></i>',C.style.background="#28a745",C.style.color="#fff",setTimeout(()=>{C.innerHTML=E,C.style.background="",C.style.color=""},2e3)}else{const C=document.createElement("textarea");C.value=window.location.href,document.body.appendChild(C),C.select(),document.execCommand("copy"),document.body.removeChild(C);const E=y.target.closest("button"),U=E.innerHTML;E.innerHTML='<i class="fas fa-check"></i>',E.style.background="#28a745",E.style.color="#fff",setTimeout(()=>{E.innerHTML=U,E.style.background="",E.style.color=""},2e3)}}catch(C){console.error("Paylaşım hatası:",C)}},className:"share-button copy","aria-label":"Linki kopyala veya paylaş",title:"Linki kopyala",children:e("i",{className:"fas fa-copy"})})]})]})})]}),e(wn,{category:d.kategori,excludeHaberKodu:d.haber_kodu})]})})}):e("div",{className:"container",children:e("div",{className:"alert alert-warning",children:"Haber bulunamadı."})})},Tn=()=>{const{getRelativeTime:t,truncateText:a}=Pe(),{data:i,isLoading:r}=ye("categories",()=>fe.getCategories(),{staleTime:6e5}),{data:n,isLoading:s}=ye("popular-news",()=>fe.getNews({limit:5}),{staleTime:3e5}),{data:l,isLoading:o}=ye("latest-news",()=>fe.getNews({limit:5}),{staleTime:3e5});return c("aside",{className:"sidebar",children:[c("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:"Kategoriler"}),c("div",{className:"category-list",children:[c(z,{to:"/yerel-haberler",className:"category-item featured-category",children:[c("span",{children:[e("i",{className:"fas fa-map-marker-alt"})," Yerel Haberler"]}),e("span",{className:"category-badge",children:"YENİ"})]}),r?e("div",{className:"text-center py-3",children:e(he,{})}):i!=null&&i.success&&i.data?i.data.map(u=>c(z,{to:`/kategori/${encodeURIComponent(u.name)}`,className:"category-item",children:[e("span",{children:bt(u.name)}),e("span",{className:"category-count",children:u.count})]},u.name)):e("p",{className:"text-muted",children:"Kategoriler yüklenemedi"})]})]}),c("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:"Popüler Haberler"}),e("div",{className:"popular-news",children:s?e("div",{className:"text-center py-3",children:e(he,{})}):n!=null&&n.success&&n.data?n.data.slice(0,5).map((u,h)=>e("article",{className:"news-item",children:c(z,{to:`/haber/${u.haber_kodu}`,className:"news-link",children:[e("div",{className:"news-number",children:h+1}),e(Me.LazyLoadImage,{src:u.featured_image||"/assets/images/placeholder.svg",alt:u.title,className:"news-image",effect:"blur",onError:m=>{m.target.src="/assets/images/placeholder.svg"}}),c("div",{className:"news-content",children:[e("h4",{className:"news-title",children:a(u.title,60)}),c("div",{className:"news-meta",children:[e("span",{className:"news-date",children:u.formatted_date||t(u.pub_date)}),c("span",{className:"news-views",children:[e("i",{className:"far fa-eye me-1"}),u.view_count]})]})]})]})},u.haber_kodu)):e("div",{className:"alert alert-info",children:"Popüler haber bulunamadı."})})]}),c("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:"Son Eklenen"}),e("div",{className:"latest-news",children:o?e("div",{className:"text-center py-3",children:e(he,{})}):l!=null&&l.success&&Array.isArray(l.data)?l.data.slice(0,5).map(u=>e("article",{className:"news-item",children:c(z,{to:`/haber/${u.haber_kodu}`,className:"news-link",children:[e(Me.LazyLoadImage,{src:u.featured_image||"/assets/images/placeholder.svg",alt:u.title,className:"news-image",effect:"blur",onError:h=>{h.target.src="/assets/images/placeholder.svg"}}),c("div",{className:"news-content",children:[e("h4",{className:"news-title",children:a(u.title,60)}),c("div",{className:"news-meta",children:[e("span",{className:"category-badge",children:u.kategori}),e("span",{className:"news-date",children:u.formatted_date||t(u.pub_date)})]})]})]})},u.haber_kodu)):e("div",{className:"alert alert-info",children:"Son haber bulunamadı."})})]}),c("div",{className:"sidebar-widget",children:[e("h3",{className:"widget-title",children:e("span",{className:"breaking-badge",children:"SON DAKİKA"})}),e("div",{className:"breaking-news",children:e("div",{className:"alert alert-warning",children:"Son dakika haberleri yükleniyor..."})})]})]})},Sn=()=>{var N,v,d,w;const[t]=Hr(),[a,i]=P.useState(1),[r,n]=P.useState([]),s=t.get("q")||"";P.useEffect(()=>{i(1),n([])},[s]);const{data:l,isLoading:o,error:u,refetch:h}=ye(["search",s,a],()=>fe.searchNews(s,{page:a,limit:12}),{enabled:!!s,keepPreviousData:!0,onSuccess:A=>{var S;A.success&&((S=A.data)!=null&&S.data)&&n(a===1?A.data.data:b=>[...b,...A.data.data])}}),m=()=>{var A,S;((A=l==null?void 0:l.pagination)==null?void 0:A.current_page)<((S=l==null?void 0:l.pagination)==null?void 0:S.total_pages)&&i(b=>b+1)},f=((N=l==null?void 0:l.pagination)==null?void 0:N.current_page)<((v=l==null?void 0:l.pagination)==null?void 0:v.total_pages);return s?u?e("div",{className:"container",children:e(et,{message:"Arama sonuçları yüklenirken bir hata oluştu.",onRetry:h})}):c(ie,{children:[c(Ee,{children:[c("title",{children:[s," - Arama Sonuçları | Meta Analiz Haber"]}),e("meta",{name:"description",content:`"${s}" için arama sonuçları. Meta Analiz Haber'de ${s} ile ilgili haberler.`})]}),c("div",{className:"container",children:[c("div",{className:"page-header",children:[c("h1",{className:"page-title",children:['"',s,'" için Arama Sonuçları']}),e("div",{className:"page-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(z,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:"Arama"})]})})})]}),c("div",{className:"row",children:[e("div",{className:"col-lg-8",children:e("section",{className:"search-results",children:o&&a===1?e("div",{className:"text-center py-5",children:e(he,{})}):((w=(d=l==null?void 0:l.data)==null?void 0:d.data)==null?void 0:w.length)===0?c("div",{className:"alert alert-warning",children:[e("h4",{children:"Sonuç bulunamadı"}),c("p",{children:['"',s,'" için herhangi bir haber bulunamadı.']}),e("p",{children:"Öneriler:"}),c("ul",{children:[e("li",{children:"Farklı kelimeler deneyin"}),e("li",{children:"Daha genel terimler kullanın"}),e("li",{children:"Yazım hatası olup olmadığını kontrol edin"})]})]}):c(ie,{children:[(l==null?void 0:l.pagination)&&e("div",{className:"search-info mb-4",children:c("p",{className:"text-muted",children:[e("strong",{children:l.pagination.total_count})," sonuç bulundu",l.pagination.total_pages>1&&c("span",{children:[" (Sayfa ",l.pagination.current_page," / ",l.pagination.total_pages,")"]})]})}),e(Tt,{news:r}),f&&e("div",{className:"text-center mt-4",children:e("button",{className:"btn btn-outline-primary btn-lg",onClick:m,disabled:o,children:o?c(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Sonuç Yükle"})}),o&&a>1&&e("div",{className:"text-center py-3",children:e(he,{})})]})})}),e("div",{className:"col-lg-4",children:e(Tn,{})})]})]})]}):c("div",{className:"container",children:[c("div",{className:"page-header",children:[e("h1",{className:"page-title",children:"Arama"}),e("div",{className:"page-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",children:[e("li",{className:"breadcrumb-item",children:e(z,{to:"/",children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",children:"Arama"})]})})})]}),e("div",{className:"alert alert-info",children:"Arama yapmak için bir kelime girin."})]})};const dr=()=>{const{cityName:t}=jt(),{getRelativeTime:a}=Pe(),i=_t(),[r,n]=P.useState(t||""),[s,l]=P.useState([]),[o,u]=P.useState({}),[h,m]=P.useState(1),f=20,N=["Aydın","İstanbul","Ankara","Bursa","İzmir"];P.useEffect(()=>{t?(console.log("URL'den gelen şehir:",t),n(t),i.invalidateQueries(["selected-city-news"])):(n(""),i.invalidateQueries(["local-news-main"]))},[t,i]);const{data:v,isLoading:d}=ye("cities-list",()=>fe.getCities(),{staleTime:6e5,onSuccess:y=>{y&&y.success&&Array.isArray(y.data)&&l(y.data)}}),{data:w,isLoading:A,error:S,refetch:b}=ye(["local-news-main",r],()=>fe.getNews({limit:5e3,sort:"pub_date",order:"DESC"}),{enabled:!r,staleTime:3e5,onSuccess:y=>{var C;console.log("Ana sayfa için çekilen toplam haber sayısı:",((C=y==null?void 0:y.data)==null?void 0:C.length)||0),y&&y.success&&Array.isArray(y.data)&&le(y.data)}}),{data:R,isLoading:V,error:j}=ye(["selected-city-news",r],()=>(console.log("API çağrısı yapılıyor, şehir:",r),console.log("API parametreleri:",{sehir:r,limit:1e3,sort:"pub_date",order:"DESC"}),fe.getNews({sehir:r,limit:1e3,sort:"pub_date",order:"DESC"})),{enabled:!!r,staleTime:3e5,onSuccess:y=>{var C,E;console.log("API yanıtı:",y),console.log("Bulunan haber sayısı:",((C=y==null?void 0:y.data)==null?void 0:C.length)||0),((E=y==null?void 0:y.data)==null?void 0:E.length)>0&&console.log("İlk 3 haberin şehir bilgileri:",y.data.slice(0,3).map(U=>({title:U.title.substring(0,50)+"...",sehir:U.sehir})))}}),D=y=>y.toLowerCase().replace(/ı/g,"i").replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ö/g,"o").replace(/ç/g,"c").trim(),le=y=>{console.log("İşlenecek toplam haber sayısı:",y.length);const C=y.filter(B=>B.sehir&&B.sehir.trim()!=="");console.log("Şehir bilgisi olan haber sayısı:",C.length);const E=[...new Set(C.map(B=>B.sehir))];console.log("Veritabanındaki benzersiz şehirler:",E.slice(0,20));const U={};N.forEach(B=>{const Z=D(B);console.log(`${B} şehri için arama yapılıyor (normalize: ${Z})`);const q=C.filter(ae=>{if(!ae.sehir)return!1;const ne=D(ae.sehir);return ne===Z||ne.includes(Z)||Z.includes(ne)}).sort((ae,ne)=>new Date(ne.pub_date||ne.created_at)-new Date(ae.pub_date||ae.created_at)).slice(0,4);console.log(`${B} için bulunan haber sayısı:`,q.length),q.length>0?(console.log(`${B} haberleri:`,q.map(ae=>({title:ae.title.substring(0,50)+"...",sehir:ae.sehir,pub_date:ae.pub_date}))),U[B]=q):console.log(`${B} için hiç haber bulunamadı!`)}),u(U)},H=y=>{n(y.target.value),m(1)},Y=y=>{m(y),window.scrollTo({top:0,behavior:"smooth"})},W=()=>{n(""),m(1)},te=()=>{if(!r)return[];if(console.log("getFilteredNews çağrıldı, selectedCity:",r),R!=null&&R.success&&Array.isArray(R.data)){console.log("API'den gelen toplam haber sayısı:",R.data.length);const y=D(r);console.log("Normalize edilmiş seçili şehir:",y);const C=R.data.filter(E=>{if(!E.sehir)return console.log("Şehir bilgisi olmayan haber atlandı:",E.title.substring(0,50)),!1;const U=D(E.sehir),B=U===y||U.includes(y)||y.includes(U);return B&&console.log("Eşleşen haber:",{title:E.title.substring(0,50)+"...",sehir:E.sehir,normalizedSehir:U}),B}).sort((E,U)=>new Date(U.pub_date||U.created_at)-new Date(E.pub_date||E.created_at));return console.log("Filtrelenmiş haber sayısı:",C.length),C}return console.log("API verisi mevcut değil veya hatalı"),[]},G=()=>{const y=te(),C=(h-1)*f,E=C+f;return y.slice(C,E)},X=()=>{const y=te();return Math.ceil(y.length/f)};return c(ie,{children:[c(Ee,{children:[e("title",{children:"Yerel Haberler - Meta Analiz Haber"}),e("meta",{name:"description",content:"Türkiye'nin tüm şehirlerinden güncel yerel haberler. İl il, bölge bölge haberleri takip edin."}),e("meta",{name:"keywords",content:"yerel haberler, şehir haberleri, il haberleri, bölgesel haberler, Türkiye haberleri"})]}),c("div",{className:"local-news-page category-page",children:[e("div",{className:"page-header",children:e("div",{className:"container",children:c("div",{className:"d-flex justify-content-between align-items-start",children:[c("div",{children:[c("h1",{className:"page-title",children:[e("span",{className:"category-name",children:"Yerel"}),e("span",{className:"category-suffix",children:" Haberler"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:[e("li",{className:"breadcrumb-item",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:e(z,{to:"/",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Ana Sayfa"})}),r?c(ie,{children:[e("li",{className:"breadcrumb-item",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:e(z,{to:"/yerel-haberler",onClick:W,style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Yerel Haberler"})}),e("li",{className:"breadcrumb-item active",style:{textTransform:"capitalize",fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:r})]}):e("li",{className:"breadcrumb-item active",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Yerel Haberler"})]})})})]}),c("div",{className:"city-filter-header",children:[e("label",{htmlFor:"city-select",className:"filter-label me-2",children:"Şehir:"}),c("select",{id:"city-select",className:"form-select city-select",value:r,onChange:H,style:{width:"150px"},children:[e("option",{value:"",children:"Tüm Şehirler"}),s.map(y=>e("option",{value:y,children:y},y))]})]})]})})}),c("div",{className:"container",children:[!r&&Object.keys(o).length>0&&e("div",{className:"main-cities-sections",children:Object.entries(o).map(([y,C])=>c("div",{className:"city-section",children:[c("div",{className:"section-header",children:[c("h2",{className:"section-title",children:[y," Haberleri"]}),e(z,{to:`/yerel-haberler/${encodeURIComponent(y)}`,className:"section-link",children:"Tümünü Gör"})]}),e("div",{className:"category-news-grid",children:C.map(E=>e("article",{className:"news-card",children:c(z,{to:`/haber/${E.slug||E.haber_kodu}`,children:[c("div",{className:"card-image",children:[e("img",{src:E.main_image||E.video_poster_url||"/assets/images/placeholder.svg",alt:E.title,onError:U=>{U.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"card-category",children:"Yerel"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:E.title}),e("div",{className:"card-date",children:a(E.pub_date||E.tarih)})]})]})},E.haber_kodu))})]},y))}),r&&c("div",{className:"selected-city-news",children:[c("div",{className:"section-header",children:[c("h2",{className:"section-title",children:[r," Haberleri"]}),c("span",{className:"news-count",children:[te().length," haber - Sayfa ",h,"/",X()]})]}),V?e("div",{className:"text-center py-5",children:e(he,{text:"Haberler yükleniyor..."})}):j?c("div",{className:"alert alert-danger text-center",children:[e("i",{className:"fas fa-exclamation-triangle me-2"}),"Haberler yüklenirken bir hata oluştu."]}):G().length===0?c("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),r," için haber bulunamadı."]}):c(ie,{children:[e("div",{className:"category-news-grid",children:G().map(y=>e("article",{className:"news-card",children:c(z,{to:`/haber/${y.slug||y.haber_kodu}`,children:[c("div",{className:"card-image",children:[e("img",{src:y.main_image||y.video_poster_url||"/assets/images/placeholder.svg",alt:y.title,onError:C=>{C.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"card-category",children:"Yerel"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:y.title}),e("div",{className:"card-date",children:a(y.pub_date||y.tarih)})]})]})},y.haber_kodu))}),X()>1&&e("div",{className:"pagination-container",children:e("nav",{"aria-label":"Sayfa navigasyonu",children:c("ul",{className:"pagination justify-content-center",children:[e("li",{className:`page-item ${h===1?"disabled":""}`,children:e("button",{className:"page-link",onClick:()=>Y(h-1),disabled:h===1,children:"Önceki"})}),Array.from({length:X()},(y,C)=>C+1).map(y=>e("li",{className:`page-item ${h===y?"active":""}`,children:e("button",{className:"page-link",onClick:()=>Y(y),children:y})},y)),e("li",{className:`page-item ${h===X()?"disabled":""}`,children:e("button",{className:"page-link",onClick:()=>Y(h+1),disabled:h===X(),children:"Sonraki"})})]})})})]})]}),!r&&A&&e("div",{className:"text-center py-5",children:e(he,{text:"Yerel haberler yükleniyor..."})}),!r&&!A&&!d&&Object.keys(o).length===0&&c("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Yerel haberler bulunamadı."]})]})]})]})},On=()=>{var N;const[t,a]=P.useState(1),[i,r]=P.useState([]),{setCurrentCategory:n,getRelativeTime:s}=Pe(),l=_t();P.useEffect(()=>(n("Video Haberler"),a(1),r([]),l.removeQueries(["video-news-page"]),()=>n("")),[n,l]);const{data:o,isLoading:u}=ye(["video-news-page",t],()=>fe.getVideoNews({page:t,limit:12}),{keepPreviousData:!0,enabled:!0,retry:3,retryDelay:1e3,staleTime:0,cacheTime:1e3*60*5,onSuccess:v=>{v.success&&Array.isArray(v.data)&&r(t===1?v.data:d=>[...d,...v.data])}}),h=(o==null?void 0:o.success)&&((N=o.data)==null?void 0:N.length)===12,m=u&&t===1,f=()=>{h&&!u&&a(v=>v+1)};return P.useEffect(()=>{window.scrollTo(0,0)},[]),c(ie,{children:[c(Ee,{children:[e("title",{children:"Video Haberler - Meta Analiz Haber"}),e("meta",{name:"description",content:"En güncel video haberler ve görüntülü haberler."})]}),c("div",{className:"category-page video-news-page",children:[e("div",{className:"page-header",children:c("div",{className:"container",children:[c("h1",{className:"page-title",children:[e("span",{className:"category-name",children:"Video"}),e("span",{className:"category-subtitle",children:" Haberler"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:[e("li",{className:"breadcrumb-item",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:e(z,{to:"/",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Video Haberler"})]})})})]})}),e("div",{className:"category-news-section",children:e("div",{className:"container",children:m?e("div",{className:"text-center py-5",children:e(he,{text:"Video haberler yükleniyor..."})}):u&&i.length===0?e("div",{className:"text-center py-5",children:e(he,{text:"Video haberler yükleniyor..."})}):i.length===0&&!u&&o?c("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Şu anda video haber bulunmuyor."]}):c(ie,{children:[e("div",{className:"category-news-grid",children:i.map(v=>e("article",{className:"news-card",children:c(z,{to:`/haber/${v.slug||v.haber_kodu}`,children:[c("div",{className:"card-image",children:[e("img",{src:v.video_poster_url||v.main_image||"/assets/images/placeholder.svg",alt:v.title,onError:d=>{d.target.src="/assets/images/placeholder.svg"}}),e("div",{className:"card-category",children:"Video"})]}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:v.title}),e("div",{className:"card-date",children:s(v.pub_date)})]})]})},v.haber_kodu))}),h&&e("div",{className:"text-center mt-5",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:f,disabled:u,children:u?c(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Video Haber Yükle"})}),u&&t>1&&e("div",{className:"text-center py-3",children:e(he,{})})]})})})]})]})},Cn=()=>{var A;const[t,a]=P.useState(1),[i,r]=P.useState([]),{setCurrentCategory:n,getRelativeTime:s}=Pe(),l=_t();P.useEffect(()=>(n("Dünya Haberleri"),a(1),r([]),l.removeQueries(["category-news","DÜNYA"]),l.removeQueries(["world-news-page"]),()=>n("")),[n,l]);const{data:o,isLoading:u,error:h,refetch:m}=ye(["world-news-page",t],()=>fe.getWorldNews({page:t,limit:12}),{keepPreviousData:!0,enabled:!0,retry:3,retryDelay:1e3,onSuccess:S=>{S.success&&Array.isArray(S.data)&&r(t===1?S.data:b=>[...b,...S.data])}}),f=(o==null?void 0:o.success)&&((A=o.data)==null?void 0:A.length)===12,N=u&&t===1,v=i.filter(S=>{const b=S.main_image&&S.main_image!=="/assets/images/placeholder.svg"&&S.main_image.trim()!=="",R=S.video_poster_url&&S.video_poster_url.trim()!=="";return b||R}),d=i.filter(S=>{const b=S.main_image&&S.main_image!=="/assets/images/placeholder.svg"&&S.main_image.trim()!=="",R=S.video_poster_url&&S.video_poster_url.trim()!=="";return!b&&!R}),w=()=>{f&&!u&&a(S=>S+1)};return P.useEffect(()=>{window.scrollTo(0,0)},[]),c(ie,{children:[c(Ee,{children:[e("title",{children:"Dünya Haberleri - Meta Analiz Haber"}),e("meta",{name:"description",content:"Dünyadan en güncel haberler ve uluslararası gelişmeler."})]}),c("div",{className:"category-page",children:[e("div",{className:"page-header",children:c("div",{className:"container",children:[c("h1",{className:"page-title",children:[e("span",{className:"category-name",children:"Dünya"}),e("span",{className:"category-subtitle",children:" Haberleri"})]}),e("div",{className:"category-breadcrumb",children:e("nav",{"aria-label":"breadcrumb",children:c("ol",{className:"breadcrumb",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:[e("li",{className:"breadcrumb-item",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:e(z,{to:"/",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Ana Sayfa"})}),e("li",{className:"breadcrumb-item active",style:{fontFamily:'"Times New Roman", Times, serif',fontStyle:"italic"},children:"Dünya Haberleri"})]})})})]})}),e("div",{className:"category-news-section",children:e("div",{className:"container",children:N?e("div",{className:"text-center py-5",children:e(he,{text:"Dünya haberleri yükleniyor..."})}):i.length===0&&!u?c("div",{className:"alert alert-info text-center",children:[e("i",{className:"fas fa-info-circle me-2"}),"Şu anda dünya haberi bulunmuyor."]}):c(ie,{children:[c("div",{className:"world-category-layout",children:[v.length>0&&e("div",{className:"world-news-grid",children:v.map(S=>e("article",{className:"news-card",children:c(z,{to:`/haber/${S.slug||S.haber_kodu}`,children:[e("div",{className:"card-image",children:e("img",{src:S.video_poster_url||S.main_image||"/assets/images/placeholder.svg",alt:S.title,onError:b=>{b.target.src="/assets/images/placeholder.svg"}})}),c("div",{className:"card-content",children:[e("h3",{className:"card-title",children:S.title}),e("div",{className:"card-date",children:s(S.pub_date)})]})]})},S.haber_kodu))}),d.length>0&&c("div",{className:"world-news-list",children:[e("h4",{className:"world-list-title",children:"Diğer Dünya Haberleri"}),d.map(S=>e("div",{className:"world-list-item",children:c(z,{to:`/haber/${S.slug||S.haber_kodu}`,className:"world-list-content",children:[e("h4",{className:"world-list-title-text",children:S.title}),e("div",{className:"world-list-meta",children:e("span",{className:"world-list-date",children:s(S.pub_date)})})]})},S.haber_kodu))]})]}),f&&e("div",{className:"text-center mt-5",children:e("button",{className:"btn btn-outline-primary btn-lg px-5",onClick:w,disabled:u,children:u?c(ie,{children:[e("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Yükleniyor..."]}):"Daha Fazla Dünya Haberi Yükle"})}),u&&t>1&&e("div",{className:"text-center py-3",children:e(he,{})})]})})})]})]})},En=()=>e("div",{className:"container",children:c("div",{className:"page-content",children:[e("div",{className:"page-header",children:e("h1",{className:"page-title",children:"KÜNYE BİLGİLERİ"})}),e("div",{className:"kunye-content",children:c("div",{className:"kunye-table",children:[c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"Ticaret Ünvanı:"}),e("div",{className:"kunye-value",children:"Meta Analiz Haber"})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"Yayıncı:"}),e("div",{className:"kunye-value",children:"Meta Analiz Group Müşavirlik Danışmanlık Ltd. Şti."})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"Genel Yayın Yönetmeni:"}),e("div",{className:"kunye-value",children:"Özgür Atacan"})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"İmtiyaz Sahibi:"}),e("div",{className:"kunye-value",children:"Ebru Atacan - Yiğit Atacan"})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"Sorumlu Yazı İşleri Müdürü:"}),e("div",{className:"kunye-value",children:"Özgür Atacan"})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"Yönetim Yeri:"}),c("div",{className:"kunye-value",children:["Yenicami Mah. Özmen Sok. No: 24/A",e("br",{}),"Söke/ Aydın"]})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"İletişim Telefonu:"}),e("div",{className:"kunye-value",children:e("a",{href:"tel:+905423800050",className:"kunye-link",children:"+90 (542) 380 00 50"})})]}),c("div",{className:"kunye-row",children:[e("div",{className:"kunye-label",children:"Kurumsal E – Posta:"}),e("div",{className:"kunye-value",children:e("a",{href:"mailto:<EMAIL>",className:"kunye-link",children:"<EMAIL>"})})]})]})})]})}),Pn=()=>e("div",{className:"container",children:c("div",{className:"page-content",children:[e("div",{className:"page-header",children:e("h1",{className:"page-title",children:"GİZLİLİK POLİTİKASI"})}),c("div",{className:"policy-content",children:[e("div",{className:"policy-section",children:e("p",{className:"policy-intro",children:"İşbu Gizlilik Bildirimi, web sitelerimizi, uygulamalarımızı ve diğer dijital platformlarımızı kullanan veya e-posta aboneliğine sahip olan ve bizimle kişisel bilgilerini paylaşan kullanıcılar için geçerlidir."})}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Kişisel Verilerin Paylaşımı"}),e("p",{className:"policy-text",children:"Kullanıcıya / Üyeye ait kişisel veriler, yürürlükte olan mevzuata uygun olarak, şirketin doğrudan ve/veya dolaylı olarak hissedarı olduğu tüm iştirakleri ve bağlı şirketleri ile paylaşılabilir."}),e("p",{className:"policy-text",children:"Bu sayılanlar ile sınırlı olmamak üzere; iş ortaklarımıza, iş bağlantılarımıza, ifa yardımcılarımıza ve alt yüklenicilerimiz ile sunduğumuz hizmetin amacı doğrultusunda ya da düzenleyici denetleyici kurumlara ve resmi mercilerle ilgili mevzuatın öngördüğü durumlarda yurt içinde ve/veya yurt dışına aktarılabilecektir."}),e("p",{className:"policy-text",children:"Kullanıcı/Üye, kişisel verilerinin yukarıda belirtilen şekilde aktarılmasına onay verdiğini kabul ve beyan etmektedir."})]}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Bilgi Güvenliği ve Sorumluluk"}),e("p",{className:"policy-text",children:"Meta Analiz Haber sitesi aracılığıyla kişisel bilgilerinizi göndererek, bu bilgilerin saklanmasına, işlenmesine ve kullanılmasına önceden izin vermiş olursunuz. Meta Analiz Haber, gerektiğinde yetkili makamlara her türlü bilgiyi açıklama hakkını saklı tutar."}),e("p",{className:"policy-text",children:"Meta Analiz Haber yönetimi, bu web sitesi aracılığıyla gönderdiğiniz verilerin doğruluğundan yalnızca sizi sorumlu tutar."}),e("p",{className:"policy-text",children:"Meta Analiz Haber yönetimi, elektronik depolama ve uygun güvenlik teknolojilerini kullanılarak gönderilen kişisel verilerinizi korumaya gayret eder."})]}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Üçüncü Taraf Bağlantıları"}),e("p",{className:"policy-text",children:"Meta Analiz Haber sitesi, farklı web sitelerine veya portallara yönlendirilen bağlantılar içermektedir. Bu web siteleri ve portalların gizliliği koruma yöntemleri, bizim kullandığımız yöntemlerden farklı olabilir. Bu nedenle diğer sitelerin gizlilik içeriğinden ve yöntemlerinden sorumlu değiliz. Bu sitelerin gizlilik bildirimlerine başvurmanızı öneririz."})]}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Telif Hakları"}),e("p",{className:"policy-text",children:"www.metaanalizhaber.com'un tüm hakları Meta Analiz Group Müşavirlik Danışmanlık Ltd. Şti.'ye aittir."}),e("p",{className:"policy-text",children:"Bu gizlilik politikası, Meta Analiz Haber tarafından hazırlanmış olup, herhangi bir zamanda güncellenebilir. Güncellemeler bu sayfada yayınlanacaktır."})]}),e("div",{className:"policy-footer",children:c("p",{className:"policy-update",children:[e("strong",{children:"Son Güncelleme:"})," ",new Date().toLocaleDateString("tr-TR")]})})]})]})}),An=()=>e("div",{className:"container",children:c("div",{className:"page-content",children:[e("div",{className:"page-header",children:e("h1",{className:"page-title",children:"KULLANICI POLİTİKALARI"})}),c("div",{className:"policy-content",children:[c("div",{className:"policy-section",children:[e("p",{className:"policy-intro",children:'Bu web sitesi, "Meta Analiz Haber" yönetimine aittir.'}),e("p",{className:"policy-text",children:'Kişisel kullanımınıza uygun şekilde "Meta Analiz Haber Web Sitesi" veya "Web Sitesi" olarak tanımlanabilir.'}),e("p",{className:"policy-text",children:"Bu siteye erişiminiz ve bu siteyi kullanmanız, aşağıda yer alan bir takım kullanım koşullarına tabidir. Bu siteye erişiminiz ve siteye giriş yapmanız halinde, kayıtlı bir kullanıcı veya yalnızca bir ziyaretçi olup olmadığınız gözetilmeksizin, bu kullanım koşullarını kayıtsız ve şartsız bir şekilde kabul ettiğinizi teyit etmiş olursunuz. Bu teyit, siteyi kullandığınız andan itibaren geçerlidir."})]}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Koşulların Değiştirilmesi"}),e("p",{className:"policy-text",children:"Bu şartlar ve koşullarda yapılacak herhangi bir değişiklik, aksi belirtilmedikçe ilanından hemen sonra yürürlüğe girecektir. Yapılacak herhangi bir değişikliğin ilanından sonra bu siteyi kullanmaya devam etmeniz, bu değişikliği tam olarak kabul ettiğiniz anlamına gelir."}),e("p",{className:"policy-text",children:"Aşağıdaki maddeler ve kullanıcı koşulları, Gizlilik Politikası ve Fikri Mülkiyet Hakları özel bölümünü içerir."})]}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Kullanım Koşulları"}),e("p",{className:"policy-text",children:"Bu siteyi (Meta Analiz Haber) kullanmanız halinde;"}),c("ol",{className:"policy-list",children:[e("li",{className:"policy-list-item",children:"Web sitesi yönetiminden izin almadığınız hiçbir içerik, materyal, veri, resim veya diğer bilgiyi depolamamayı, indirmemeyi ve kopyalamamayı,"}),e("li",{className:"policy-list-item",children:"Bu siteyi herhangi bir ticari e-posta veya istenmeyen e-posta gönderme yolu olarak kullanmamayı ya da benzer bir şekilde suistimal etmemeyi,"}),e("li",{className:"policy-list-item",children:"Bu sitedeki virüslü veya bozuk veri içeren dosyaları depolamamayı ve indirmemeyi,"}),e("li",{className:"policy-list-item",children:"Site içeriğini yasadışı veya uygunsuz bir amaç için, başkalarının haklarını ihlal edebilecek veya herhangi bir kişiye zarar verebilecek, rahatsızlık yaratabilecek şekilde ya da Meta Analiz Haber web sitesi aracılığıyla yasadışı olarak bilgiler yayınlamamayı, duyurmamayı, dağıtmamayı veya dolaşıma koymamayı,"}),e("li",{className:"policy-list-item",children:"Bir takım ihlallerde bulunarak yasa dışı faaliyetlere katılmamayı,"}),e("li",{className:"policy-list-item",children:"Sitenin herhangi bir bölümünde geçerli yasalar veya düzenlemeleri ihlal etmemize neden olabilecek herhangi bir ürün veya hizmetin reklamını yapmamayı,"}),e("li",{className:"policy-list-item",children:"Sitenin doğru çalışmasını engellemek veya engellemeye çalışmak için herhangi bir program veya araç kullanmamayı,"}),e("li",{className:"policy-list-item",children:"Sitenin altyapısına makul olmayan veya orantısız bir şekilde yük getiren herhangi bir işlemde bulunmamayı kabul etmiş olursunuz."})]})]}),c("div",{className:"policy-section",children:[e("h2",{className:"policy-subtitle",children:"Telif Hakları"}),e("p",{className:"policy-text",children:"www.metaanalizhaber.com'un tüm hakları Meta Analiz Group Müşavirlik Danışmanlık Ltd. Şti.'ye aittir."}),e("p",{className:"policy-text",children:"Bu kullanım koşulları, Meta Analiz Haber tarafından hazırlanmış olup, herhangi bir zamanda güncellenebilir. Güncellemeler bu sayfada yayınlanacaktır."})]}),e("div",{className:"policy-footer",children:c("p",{className:"policy-update",children:[e("strong",{children:"Son Güncelleme:"})," ",new Date().toLocaleDateString("tr-TR")]})})]})]})}),Rn=()=>c(ie,{children:[c(Ee,{children:[e("title",{children:"Sayfa Bulunamadı - Meta Analiz Haber"}),e("meta",{name:"description",content:"Aradığınız sayfa bulunamadı."})]}),e("div",{className:"container",children:c("div",{className:"not-found-page text-center py-5",children:[e("p",{className:"error-message",children:"Aradığınız sayfa bulunamadı veya taşınmış olabilir."}),e("div",{className:"error-actions mt-4",children:c(z,{to:"/",className:"btn btn-primary btn-lg",children:[e("i",{className:"fas fa-home me-2"}),"Ana Sayfaya Dön"]})})]})})]});function xn(){return e(cn,{children:c("div",{className:"App",children:[c(Ee,{children:[e("title",{children:"Meta Analiz Haber - Güncel Haberler"}),e("meta",{name:"description",content:"Türkiye'den ve dünyadan en güncel haberler"}),e("link",{rel:"me",href:"https://sosyal.teknofest.app/@metaanalizgroup"}),e("a",{href:"https://sosyal.teknofest.app/@metaanalizgroup",rel:"me",style:{display:"none"},children:"Mastodon"})]}),e(sn,{}),c("main",{className:"main-content",children:[e(on,{}),c(Ur,{children:[e(_e,{path:"/",element:e(fn,{})}),e(_e,{path:"/kategori/:categoryName",element:e(gn,{})}),e(_e,{path:"/kategori/video",element:e(vn,{})}),e(_e,{path:"/son-haberler",element:e(bn,{})}),e(_e,{path:"/cron-monitor",element:e(Nn,{})}),e(_e,{path:"/yerel-haberler",element:e(dr,{})}),e(_e,{path:"/yerel-haberler/:cityName",element:e(dr,{})}),e(_e,{path:"/video-haberler",element:e(On,{})}),e(_e,{path:"/dunya-haberleri",element:e(Cn,{})}),e(_e,{path:"/haber/:newsSlug",element:e(_n,{})}),e(_e,{path:"/arama",element:e(Sn,{})}),e(_e,{path:"/kunye-bilgileri",element:e(En,{})}),e(_e,{path:"/gizlilik-politikasi",element:e(Pn,{})}),e(_e,{path:"/kullanici-politikalari",element:e(An,{})}),e(_e,{path:"*",element:e(Rn,{})})]})]}),e(ln,{})]})})}const Ln=new pa({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:1,staleTime:3e5}}});Rt.createRoot(document.getElementById("root")).render(e(Br,{children:e(Na,{client:Ln,children:e(Qe,{children:e(xn,{})})})}));
