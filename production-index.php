<?php
/**
 * Root Index - React Router Desteği
 * Production Version - Lokal ile senkronize edilmiş
 */

// Basit template fonksiyonu direkt burada tanımlanacak

$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestPath = parse_url($requestUri, PHP_URL_PATH);

// Backend API istekleri - sadece yönlendir
if (strpos($requestPath, '/backend/') === 0) {
    $backendPath = substr($requestPath, 9); // '/backend/' kısmını çıkar
    $backendFile = __DIR__ . '/backend/' . $backendPath;

    if (file_exists($backendFile) && is_file($backendFile)) {
        // PHP dosyası ise include et
        if (pathinfo($backendFile, PATHINFO_EXTENSION) === 'php') {
            chdir(__DIR__ . '/backend');
            include $backendFile;
        } else {
            readfile($backendFile);
        }
        exit;
    }

    http_response_code(404);
    echo "Backend file not found: " . $backendPath;
    exit;
}

// Assets dosyaları - root'ta
if (strpos($requestPath, '/assets/') === 0) {
    $assetFile = __DIR__ . $requestPath;  // Direkt root'tan al

    if (file_exists($assetFile) && is_file($assetFile)) {
        // MIME type belirle
        $extension = strtolower(pathinfo($assetFile, PATHINFO_EXTENSION));
        $mimeTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'ico' => 'image/x-icon'
        ];

        if (isset($mimeTypes[$extension])) {
            header('Content-Type: ' . $mimeTypes[$extension]);
        }

        // Cache headers
        header('Cache-Control: public, max-age=31536000');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');

        readfile($assetFile);
        exit;
    }

    http_response_code(404);
    echo "Asset not found";
    exit;
}

// Favicon - root'ta
if ($requestPath === '/favicon.ico') {
    $faviconFile = __DIR__ . '/favicon.ico';
    if (file_exists($faviconFile)) {
        header('Content-Type: image/x-icon');
        readfile($faviconFile);
    } else {
        http_response_code(404);
    }
    exit;
}

// React Router için - Dinamik Meta Tag Injection
// Production: index.html direkt root'ta
$frontendIndex = __DIR__ . '/index.html';

if (!file_exists($frontendIndex)) {
    http_response_code(500);
    echo "Frontend index.html not found at: " . htmlspecialchars($frontendIndex);
    echo "<br>Current directory: " . htmlspecialchars(__DIR__);
    echo "<br>Directory contents:<br>";
    if (is_dir(__DIR__)) {
        $files = scandir(__DIR__);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                echo "- " . htmlspecialchars($file) . (is_dir(__DIR__ . '/' . $file) ? ' (DIR)' : '') . "<br>";
            }
        }
    }
    exit;
}

// Haber detay sayfası için dinamik meta tag'ler
error_log("Request Path: " . $requestPath);

// Debug mode kontrolü
$debugMode = isset($_GET['debug']) && $_GET['debug'] == '1';

// Hem /haber/slug hem de /slug formatlarını destekle
if (preg_match('/^\/haber\/(.+)$/', $requestPath, $matches)) {
    $newsSlug = $matches[1];
    error_log("News Slug (with /haber/): " . $newsSlug);
} elseif (preg_match('/^\/([^\/]+)\/?$/', $requestPath, $matches)) {
    // Sadece slug formatı - ama sistem sayfalarını hariç tut
    $potentialSlug = $matches[1];

    // Sistem sayfalarını hariç tut
    $systemPages = ['page', 'author', 'wp-content', 'assets', 'backend', 'uploads', 'kategori', 'video-haberler', 'dunya-haberleri', 'yerel-haberler', 'kunye-bilgileri', 'gizlilik-politikasi', 'kullanici-politikalari'];

    $isSystemPage = false;
    foreach ($systemPages as $systemPage) {
        if (strpos($potentialSlug, $systemPage) === 0) {
            $isSystemPage = true;
            break;
        }
    }

    if (!$isSystemPage && strlen($potentialSlug) > 3) {
        $newsSlug = $potentialSlug;
        error_log("News Slug (direct): " . $newsSlug);
    } else {
        error_log("Skipped system page or short slug: " . $potentialSlug);
        $newsSlug = null;
    }
} else {
    $newsSlug = null;
}

if ($newsSlug) {

    // User Agent kontrolü - Bot detection
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $isBot = isSocialMediaBot($userAgent);

    // SEO bot'larını detect et (log'lamamak için)
    $isSEOBot = isSEOBot($userAgent);

    // Sadece sosyal medya bot'ları için log yaz (SEO bot'larını hariç tut)
    if (!$isSEOBot) {
        // X Bot detection için özel log
        if (stripos($userAgent, 'twitter') !== false || stripos($userAgent, 'x.com') !== false || stripos($userAgent, 'twitterbot') !== false) {
            error_log("🐦 X/TWITTER BOT DETECTED: " . $userAgent);
            error_log("🐦 X Bot Is Detected: " . ($isBot ? 'YES' : 'NO'));

            // X bot için dosyaya da log yaz
            $logFile = __DIR__ . '/backend/logs/x-bot-visits.log';
            $logDir = dirname($logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - X Bot Visit: " . $userAgent . " - Slug: " . $newsSlug . "\n", FILE_APPEND);
        }

        error_log("User Agent: " . $userAgent);
        error_log("Is Bot: " . ($isBot ? 'true' : 'false'));
    }

    // Haber bilgilerini veritabanından çek
    $newsData = getNewsDataBySlug($newsSlug);

    // Sadece SEO bot değilse veya haber bulunduysa log yaz
    if (!$isSEOBot || $newsData) {
        error_log("News Data: " . json_encode($newsData));
    }

    if ($newsData) {
        // Debug mode - Meta tag'leri göster
        if ($debugMode) {
            header('Content-Type: text/html; charset=utf-8');
            echo "<h1>Debug Mode - Meta Tags</h1>";
            echo "<h2>News Data:</h2>";
            echo "<pre>" . htmlspecialchars(json_encode($newsData, JSON_PRETTY_PRINT)) . "</pre>";

            $metaTags = generateMetaTags($newsData);
            echo "<h2>Generated Meta Tags:</h2>";
            echo "<pre>" . htmlspecialchars($metaTags) . "</pre>";

            echo "<h2>Image URL Validation:</h2>";
            $imageUrl = getValidImageUrl($newsData);
            echo "<p>Final Image URL: <strong>" . htmlspecialchars($imageUrl) . "</strong></p>";
            echo "<p>Image accessible: <strong>" . (isImageAccessible($imageUrl) ? 'YES' : 'NO') . "</strong></p>";

            exit;
        }

        // Bot detection - Basit template kullan
        if ($isBot) {
            header('Content-Type: text/html; charset=utf-8');

            // Sosyal medya botları için cache'e izin ver
            if (isBot()) {
                // Sosyal medya botları için cache'e izin ver
                header('Cache-Control: public, max-age=3600');
                header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
            } else {
                // Normal kullanıcılar için cache önleme
                header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
                header('Pragma: no-cache');
                header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
            }
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
            header('Vary: User-Agent, Accept-Encoding');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-Content-Type-Options: nosniff');

            // CDN bypass header'ları
            header('CF-Cache-Status: BYPASS');
            header('X-Cache: MISS');
            header('X-Served-By: direct-' . $randomId);

            // WordPress tarzı basit HTML döndür
            echo generateSimpleNewsPage($newsData);
            exit;
        }

        // Normal kullanıcılar için React SPA
        // index.html'i oku ve meta tag'leri değiştir
        $html = file_get_contents($frontendIndex);

        // Meta tag'leri inject et
        $html = injectNewsMetaTags($html, $newsData);

        header('Content-Type: text/html; charset=utf-8');
        echo $html;
        exit;
    } else {
        // Sadece SEO bot değilse log yaz
        if (!$isSEOBot) {
            error_log("News data not found for slug: " . $newsSlug);
        }
        http_response_code(404);
        header('Content-Type: text/html; charset=utf-8');
        echo file_get_contents($frontendIndex);
        exit;
    }
} else {
    error_log("Regex did not match for path: " . $requestPath);
}

// Normal sayfa - statik index.html
header('Content-Type: text/html; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
readfile($frontendIndex);

// Gelişmiş Bot Detection Fonksiyonu
function isSocialMediaBot($userAgent) {
    $botPatterns = [
        // Twitter/X
        'Twitterbot', 'Twitter', 'X-Bot', 'XBot', 'x.com', 'twitter.com',
        // Facebook
        'facebookexternalhit', 'facebookcatalog', 'FacebookBot',
        // LinkedIn
        'LinkedInBot', 'linkedin',
        // WhatsApp
        'WhatsApp', 'WhatsApp-Business',
        // Telegram
        'TelegramBot', 'Telegram',
        // Discord
        'Discordbot', 'Discord',
        // Slack
        'Slackbot', 'Slack-ImgProxy',
        // Pinterest
        'Pinterest', 'Pinterestbot',
        // Reddit
        'Redditbot', 'reddit',
        // Generic
        'Social', 'social', 'crawler', 'Crawler', 'bot', 'Bot', 'spider', 'Spider',
        // SEO tools
        'SemrushBot', 'AhrefsBot', 'MJ12bot', 'DotBot',
        // Other
        'SkypeUriPreview', 'ViberBot', 'LINE', 'KakaoTalk', 'WeChat'
    ];

    foreach ($botPatterns as $pattern) {
        if (stripos($userAgent, $pattern) !== false) {
            return true;
        }
    }
    return false;
}

// SEO Bot Detection Fonksiyonu - Gereksiz log'ları önlemek için
function isSEOBot($userAgent) {
    $seoBotPatterns = [
        'SemrushBot',
        'AhrefsBot',
        'MJ12bot',
        'DotBot',
        'Barkrowler',
        'BLEXBot',
        'DataForSeoBot',
        'PetalBot',
        'YandexBot',
        'BingBot',
        'Googlebot',
        'MauiBot',
        'SeznamBot',
        'BaiduSpider',
        'DuckDuckBot',
        'facebookcatalog', // Facebook catalog bot (SEO amaçlı)
        'LinkedInBot', // LinkedIn SEO bot
        'PinterestBot',
        'TelegramBot', // Telegram preview bot
        'WhatsApp', // WhatsApp preview bot
        'SkypeUriPreview',
        'Slackbot',
        'Discordbot'
    ];

    foreach ($seoBotPatterns as $pattern) {
        if (stripos($userAgent, $pattern) !== false) {
            return true;
        }
    }

    return false;
}

// Haber verilerini çeken fonksiyon - Bot'lar için optimize edilmiş
function getNewsDataBySlug($slug) {
    try {
        // Bot detection
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $isBot = isSocialMediaBot($userAgent);

        // Bot'lar için cache'li, normal kullanıcılar için cache bypass
        if ($isBot) {
            // Bot'lar için cache'li API çağrısı (timestamp yok)
            $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug);
            $timeout = 5; // Bot'lar için kısa timeout
        } else {
            // Normal kullanıcılar için cache bypass
            $apiUrl = "http://metaanalizhaber.com/backend/api.php?action=get_news_by_slug&slug=" . urlencode($slug) . "&_t=" . time();
            $timeout = 15; // Normal kullanıcılar için uzun timeout
        }

        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'ignore_errors' => true,
                'method' => 'GET',
                'header' => [
                    "User-Agent: Mozilla/5.0 (compatible; MetaAnaliz-SSR/1.0)",
                    $isBot ? "Cache-Control: max-age=3600" : "Cache-Control: no-cache",
                    $isBot ? "" : "Pragma: no-cache",
                    "X-Requested-With: XMLHttpRequest"
                ]
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $response = file_get_contents($apiUrl, false, $context);

        if ($response === false) {
            error_log("API request failed for slug: " . $slug);
            return null;
        }

        $data = json_decode($response, true);

        if ($data && $data['success'] && isset($data['data'])) {
            error_log("API success for slug: " . $slug);
            return $data['data'];
        }

        error_log("API returned no data for slug: " . $slug . " - Response: " . $response);
        return null;
    } catch (Exception $e) {
        error_log("Exception in getNewsDataBySlug: " . $e->getMessage());
        return null;
    }
}

// Bot detection fonksiyonu - isSocialMediaBot ile aynı pattern'ları kullan
function isBot() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    return isSocialMediaBot($userAgent);
}

// Meta tag injection fonksiyonu - Bot optimized
function injectNewsMetaTags($html, $newsData) {
    $title = htmlspecialchars($newsData['title'] ?? 'MetaAnaliz Haber');

    // Description'dan HTML tag'leri temizle
    $rawDescription = $newsData['description'] ?? '';
    $cleanDescription = strip_tags($rawDescription);
    $cleanDescription = html_entity_decode($cleanDescription, ENT_QUOTES, 'UTF-8');
    $cleanDescription = preg_replace('/\s+/', ' ', $cleanDescription);
    $cleanDescription = trim($cleanDescription);

    // Facebook için daha uzun açıklama (300 karakter)
    $facebookDescription = htmlspecialchars(substr($cleanDescription, 0, 300) . (strlen($cleanDescription) > 300 ? '...' : ''));
    // Twitter için kısa açıklama (160 karakter)
    $twitterDescription = htmlspecialchars(substr($cleanDescription, 0, 160) . (strlen($cleanDescription) > 160 ? '...' : ''));

    $url = 'https://metaanalizhaber.com/haber/' . ($newsData['slug'] ?? '');

    // Görsel URL'sini al - Basit ve temiz
    $image = getValidImageUrl($newsData);

    // Sosyal medya için tutarlı URL'ler kullan - cache bypass parametrelerini kaldır

    // Görsel yoksa meta tag'leri oluşturma
    if (empty($image)) {
        $socialMetaTags =
            "\n" . '<!-- No image available - Basic meta tags only -->' . "\n" .
            '<meta name="description" content="' . htmlspecialchars($facebookDescription) . '">' . "\n" .
            '<meta property="og:title" content="' . htmlspecialchars($title) . '">' . "\n" .
            '<meta property="og:description" content="' . htmlspecialchars($facebookDescription) . '">' . "\n" .
            '<meta property="og:site_name" content="MetaAnaliz Haber">' . "\n" .
            '<meta property="og:type" content="article">' . "\n" .
            '<meta property="og:url" content="' . htmlspecialchars($url) . '">' . "\n" .
            '<meta name="twitter:card" content="summary">' . "\n" .
            '<meta name="twitter:title" content="' . htmlspecialchars($title) . '">' . "\n" .
            '<meta name="twitter:description" content="' . htmlspecialchars($twitterDescription) . '">' . "\n" .
            '<meta name="twitter:site" content="@MetaAnaliz">' . "\n" .
            '<meta name="twitter:creator" content="@MetaAnaliz">' . "\n" .
            '<meta property="twitter:domain" content="metaanalizhaber.com">' . "\n" .
            '<meta property="twitter:url" content="' . htmlspecialchars($url) . '">' . "\n" .
            '<meta name="robots" content="index, follow, max-image-preview:large">' . "\n" .
            '' . "\n" .
            '<!-- Next Sosyal Platform Doğrulama -->' . "\n" .
            '<link rel="me" href="https://sosyal.teknofest.app/@metaanalizgroup">' . "\n";
    } else {
        // X botları için ULTRA optimize edilmiş meta tag sırası - Image preload ile
        $socialMetaTags =
            "\n" . '<!-- CRITICAL: Image preload for faster X bot processing -->' . "\n" .
            '<link rel="preload" href="' . htmlspecialchars($image) . '" as="image">' . "\n" .
            '' . "\n" .
            '<!-- Twitter Card meta tags - X botları için EN ÖNCELİKLİ -->' . "\n" .
            '<meta name="twitter:card" content="summary_large_image">' . "\n" .
            '<meta name="twitter:image" content="' . htmlspecialchars($image) . '">' . "\n" .
            '<meta name="twitter:title" content="' . htmlspecialchars($title) . '">' . "\n" .
            '<meta name="twitter:description" content="' . htmlspecialchars($twitterDescription) . '">' . "\n" .
            '<meta name="twitter:site" content="@MetaAnaliz">' . "\n" .
            '<meta name="twitter:creator" content="@MetaAnaliz">' . "\n" .
            '<meta name="twitter:image:alt" content="' . htmlspecialchars($title) . '">' . "\n" .
            '<meta property="twitter:domain" content="metaanalizhaber.com">' . "\n" .
            '<meta property="twitter:url" content="' . htmlspecialchars($url) . '">' . "\n" .
            '' . "\n" .
            '<!-- Open Graph meta tags -->' . "\n" .
            '<meta name="description" content="' . htmlspecialchars($facebookDescription) . '">' . "\n" .
            '<meta property="og:image" content="' . htmlspecialchars($image) . '">' . "\n" .
            '<meta property="og:title" content="' . htmlspecialchars($title) . '">' . "\n" .
            '<meta property="og:description" content="' . htmlspecialchars($facebookDescription) . '">' . "\n" .
            '<meta property="og:site_name" content="MetaAnaliz Haber">' . "\n" .
            '<meta property="og:type" content="article">' . "\n" .
            '<meta property="og:url" content="' . htmlspecialchars($url) . '">' . "\n" .
            '<meta property="og:image:width" content="1200">' . "\n" .
            '<meta property="og:image:height" content="630">' . "\n" .
            '<meta property="og:image:alt" content="' . htmlspecialchars($title) . '">' . "\n" .
            '<meta name="robots" content="index, follow, max-image-preview:large">' . "\n" .
            '' . "\n" .
            '<!-- Next Sosyal Platform Doğrulama -->' . "\n" .
            '<link rel="me" href="https://sosyal.teknofest.app/@metaanalizgroup">' . "\n";
    }



    // Mevcut sosyal medya meta tag'lerini temizle
    $html = preg_replace('/<meta name="description" content=".*?">/', '', $html);
    $html = preg_replace('/<meta property="og:.*?">/', '', $html);
    $html = preg_replace('/<meta name="twitter:.*?">/', '', $html);
    $html = preg_replace('/<meta property="twitter:.*?">/', '', $html);

    // Bot detection - X botları için ULTRA agresif optimizasyon
    if (isBot()) {
        // X botları için meta tagları head'in EN BAŞINA ekle
        $html = str_replace('<head>', '<head>' . $socialMetaTags, $html);

        // X botları için ULTRA agresif cache bypass
        $botCacheBypass = "\n" . '<!-- X Bot Cache Bypass -->' . "\n" .
                         '<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate, max-age=0">' . "\n" .
                         '<meta http-equiv="pragma" content="no-cache">' . "\n" .
                         '<meta http-equiv="expires" content="0">' . "\n" .
                         '<meta name="cache-control" content="no-cache, no-store, must-revalidate">' . "\n" .
                         '<meta name="pragma" content="no-cache">' . "\n" .
                         '<meta name="expires" content="0">' . "\n" .
                         '<meta name="robots" content="index, follow, noarchive, nosnippet">' . "\n";
        $html = str_replace($socialMetaTags, $socialMetaTags . $botCacheBypass, $html);

        // X bot için response header'ları da ekle
        header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: 0');
    } else {
        // Normal kullanıcılar için meta tagları charset'ten sonra ekle
        $html = preg_replace('/(<meta charset="UTF-8">)/', '$1' . $socialMetaTags, $html);
    }

    // Title'ı güncelle
    $html = preg_replace('/<title>.*?<\/title>/', '<title>' . $title . ' - Meta Analiz Haber</title>', $html);

    return $html;
}

// Debug için meta tag'leri generate eden fonksiyon
function generateMetaTags($newsData) {
    $title = htmlspecialchars($newsData['title'] ?? 'MetaAnaliz Haber');
    $description = htmlspecialchars(strip_tags($newsData['description'] ?? ''));
    $image = getValidImageUrl($newsData);
    $url = "https://metaanalizhaber.com/haber/" . urlencode($newsData['slug'] ?? '');

    // Görsel yoksa meta tag'leri oluşturma
    if (empty($image)) {
        return '<title>' . $title . '</title>' . "\n" .
               '<meta name="description" content="' . $description . '">' . "\n" .
               '<meta property="og:title" content="' . $title . '">' . "\n" .
               '<meta property="og:description" content="' . $description . '">' . "\n" .
               '<meta property="og:url" content="' . $url . '">' . "\n" .
               '<meta name="twitter:card" content="summary">';
    }

    $imageType = 'image/png';
    if (strpos($image, '.webp') !== false) {
        $imageType = 'image/webp';
    } elseif (strpos($image, '.jpg') !== false || strpos($image, '.jpeg') !== false) {
        $imageType = 'image/jpeg';
    }

    return '<title>' . $title . '</title>' . "\n" .
           '<meta name="description" content="' . $description . '">' . "\n" .
           '<meta property="og:title" content="' . $title . '">' . "\n" .
           '<meta property="og:description" content="' . $description . '">' . "\n" .
           '<meta property="og:image" content="' . htmlspecialchars($image . '?v=' . time() . '&fb=' . mt_rand(100000, 999999)) . '">' . "\n" .
           '<meta property="og:image:type" content="' . $imageType . '">' . "\n" .
           '<meta property="og:url" content="' . $url . '?fb_refresh=' . time() . '">' . "\n" .
           '<meta name="twitter:card" content="summary_large_image">' . "\n" .
           '<meta name="twitter:image" content="' . htmlspecialchars($image) . '">';
}

// BASIT ve DOĞRUDAN görsel URL fonksiyonu - Video poster desteği ile
function getValidImageUrl($newsData) {
    // 1. Önce haber görseli var mı kontrol et
    $images = $newsData['images'] ?? [];
    if (!empty($images) && is_array($images) && !empty($images[0])) {
        $imageUrl = $images[0];

        // Eğer relative URL ise absolute yap
        if (strpos($imageUrl, 'http') !== 0) {
            $imageUrl = 'https://metaanalizhaber.com/' . ltrim($imageUrl, '/');
        }

        return $imageUrl;
    }

    // 2. Resim yoksa video poster URL'sini kontrol et
    $videos = $newsData['videos'] ?? [];
    error_log("Videos array: " . json_encode($videos));

    if (!empty($videos) && is_array($videos) && !empty($videos[0]['poster_url'])) {
        $posterUrl = $videos[0]['poster_url'];
        error_log("Found poster URL: " . $posterUrl);

        // Video poster URL'si genelde zaten absolute olur
        if (!empty($posterUrl) && strpos($posterUrl, 'http') === 0) {
            error_log("Using poster URL: " . $posterUrl);
            return $posterUrl;
        }
    } else {
        error_log("No valid poster URL found");
    }

    // 3. Hiçbir görsel yoksa null döndür - site logosu asla kullanılmasın
    return null;
}

// Görsel erişilebilirliğini kontrol eden fonksiyon
function isImageAccessible($imageUrl) {
    $headers = @get_headers($imageUrl, 1);
    return $headers && strpos($headers[0], '200') !== false;
}

// WordPress tarzı basit haber sayfası template'i - Bot'lar için
function generateSimpleNewsPage($newsData) {
    $title = htmlspecialchars($newsData['title'] ?? 'MetaAnaliz Haber');
    $description = strip_tags($newsData['description'] ?? '');
    $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
    $description = preg_replace('/\s+/', ' ', trim($description));
    $shortDescription = substr($description, 0, 160) . (strlen($description) > 160 ? '...' : '');

    // Görsel URL'sini al
    $image = getValidImageUrl($newsData);
    $url = 'https://metaanalizhaber.com/haber/' . urlencode($newsData['slug'] ?? '');

    // Twitter için BASIT image type - PNG tercih ediyor
    $imageType = 'image/png';
    if (strpos($image, '.jpg') !== false || strpos($image, '.jpeg') !== false) {
        $imageType = 'image/jpeg';
    } elseif (strpos($image, '.webp') !== false) {
        $imageType = 'image/webp';
    }

    // WordPress tarzı basit HTML - Sadece gerekli meta tag'ler
    return '<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . ' - MetaAnaliz Haber</title>

    <!-- KRITIK: Description meta tag en başta -->
    <meta name="description" content="' . htmlspecialchars($shortDescription) . '">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="article">
    <meta property="og:title" content="' . $title . '">
    <meta property="og:description" content="' . htmlspecialchars($shortDescription) . '">
    <meta property="og:url" content="' . $url . '?fb_refresh=' . time() . '">
    <meta property="og:site_name" content="MetaAnaliz Haber">
    <meta property="og:image" content="' . htmlspecialchars($image . '?v=' . time() . '&fb=' . mt_rand(100000, 999999)) . '">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="' . $imageType . '">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="' . $title . '">
    <meta name="twitter:description" content="' . htmlspecialchars($shortDescription) . '">
    <meta name="twitter:image" content="' . htmlspecialchars($image) . '">
    <meta name="twitter:site" content="@MetaAnaliz">

    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .title { font-size: 24px; font-weight: bold; margin: 20px 0; color: #333; }
        .image { width: 100%; max-width: 600px; height: auto; margin: 20px 0; }
        .content { font-size: 16px; line-height: 1.6; color: #444; }
        .footer { text-align: center; margin-top: 40px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">' . $title . '</h1>
        <img src="' . htmlspecialchars($image) . '" alt="' . $title . '" class="image">
        <div class="content">' . $description . '</div>
        <div class="footer">
            <p>&copy; 2025 MetaAnaliz Haber</p>
            <p><a href="' . $url . '">Ana siteye git</a></p>
        </div>
    </div>
</body>
</html>';
}
